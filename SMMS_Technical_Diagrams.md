# 📊 SMMS Technical Diagrams & Visual Documentation

## 🏗️ System Architecture Diagrams

### 1. High-Level System Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        A[React Frontend<br/>TypeScript + Tailwind]
        B[Mobile Browser<br/>Responsive Design]
    end
    
    subgraph "API Gateway"
        C[.NET 8 Web API<br/>RESTful Endpoints]
        D[SignalR Hub<br/>Real-time Communication]
    end
    
    subgraph "Application Layer"
        E[Authentication Service<br/>JWT + OAuth]
        F[Medical Service<br/>Stock + Requests]
        G[User Service<br/>Management + Profiles]
        H[Notification Service<br/>Multi-channel Alerts]
    end
    
    subgraph "Infrastructure Layer"
        I[Entity Framework Core<br/>ORM + Migrations]
        J[SQL Server Database<br/>Relational Data]
        K[Redis Cache<br/>Performance Layer]
        L[File Storage<br/>Cloudinary CDN]
    end
    
    subgraph "External Services"
        M[Firebase<br/>SMS/OTP Service]
        N[Google OAuth<br/>Authentication]
        O[SMTP Server<br/>Email Notifications]
    end
    
    A --> C
    B --> C
    C --> E
    C --> F
    C --> G
    C --> H
    D --> H
    E --> I
    F --> I
    G --> I
    H --> I
    I --> J
    I --> K
    H --> L
    E --> M
    E --> N
    H --> O
```

### 2. Database Entity Relationship Diagram

```mermaid
erDiagram
    User ||--o{ Student : "has children"
    User ||--|| Role : "assigned to"
    User ||--o{ MedicalRequest : "creates"
    User ||--o{ Blog : "authors"
    User ||--o{ Notification : "receives"
    
    Student ||--|| SchoolClass : "belongs to"
    Student ||--o{ HealthProfile : "has history"
    Student ||--o{ MedicalRequest : "receives medication"
    Student ||--o{ VaccinationRecord : "has vaccinations"
    Student ||--o{ MedicalIncident : "involved in"
    Student ||--o{ ActivityConsent : "requires consent"
    
    MedicalRequest ||--o{ MedicationRequestAdministration : "administered"
    MedicalStock ||--o{ MedicalUsage : "tracks usage"
    
    HealthActivity ||--o{ HealthActivityClass : "targets classes"
    SchoolClass ||--o{ HealthActivityClass : "participates in"
    
    VaccinationCampaign ||--o{ VaccinationCampaignClass : "targets classes"
    SchoolClass ||--o{ VaccinationCampaignClass : "participates in"
    
    HealthActivity ||--o{ ActivityConsent : "requires consent"
    VaccinationCampaign ||--o{ ActivityConsent : "requires consent"
    
    User {
        string Id PK
        string RoleId FK
        string Email
        string Phone
        string Password
        string FullName
        string Image
        datetime CreatedTime
        string CreatedBy
    }
    
    Student {
        string Id PK
        int StudentNumber
        string StudentCode
        string ParentId FK
        string ClassId FK
        string FullName
        string Gender
        datetime DateOfBirth
        string Image
    }
    
    MedicalRequest {
        string Id PK
        string StudentId FK
        string UserId FK
        string MedicationName
        string Dosage
        int Frequency
        int TotalQuantity
        int RemainingQuantity
        string TimeToAdminister
        datetime StartDate
        datetime EndDate
        string Status
    }
```

### 3. User Authentication Flow

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant A as API
    participant DB as Database
    participant FB as Firebase
    participant G as Google OAuth
    
    Note over U,G: Email/Password Login
    U->>F: Enter credentials
    F->>A: POST /auth/login
    A->>DB: Validate user
    DB-->>A: User data + role
    A->>A: Generate JWT token
    A-->>F: Return token + user info
    F->>F: Store token in localStorage
    F-->>U: Redirect to dashboard
    
    Note over U,G: Phone/OTP Login
    U->>F: Enter phone number
    F->>A: POST /auth/verify-phonenumber
    A->>FB: Send OTP via SMS
    FB-->>U: SMS with OTP code
    U->>F: Enter OTP code
    F->>A: POST /auth/verify-otp
    A->>FB: Verify OTP
    FB-->>A: OTP validation result
    A->>A: Generate JWT token
    A-->>F: Return token + user info
    
    Note over U,G: Google OAuth Login
    U->>F: Click Google login
    F->>G: Initiate OAuth flow
    G-->>U: Google login page
    U->>G: Enter Google credentials
    G-->>F: Return OAuth token
    F->>A: POST /auth/login-google
    A->>G: Validate OAuth token
    G-->>A: User email verification
    A->>DB: Find user by email
    DB-->>A: User data
    A->>A: Generate JWT token
    A-->>F: Return token + user info
```

### 4. Medical Request Processing Workflow

```mermaid
flowchart TD
    A[Medical Request Created] --> B{Request Type}
    B -->|New Medication| C[Check Medical Stock]
    B -->|Existing Medication| D[Validate Student Profile]
    
    C --> E{Stock Available?}
    E -->|Yes| F[Create Request Record]
    E -->|No| G[Generate Stock Alert]
    G --> H[Notify Admin/Manager]
    H --> I[Restock Required]
    
    D --> F
    F --> J[Assign to Medical Staff]
    J --> K[Medical Staff Review]
    K --> L{Approval Decision}
    
    L -->|Approved| M[Generate Medication Schedule]
    L -->|Rejected| N[Notify Parent with Reason]
    L -->|Needs More Info| O[Request Additional Information]
    
    M --> P[Daily Medication Calendar]
    P --> Q[Nurse Administration]
    Q --> R[Record Administration]
    R --> S{Medication Complete?}
    
    S -->|Yes| T[Mark Request Complete]
    S -->|No| U[Update Remaining Quantity]
    U --> P
    
    T --> V[Notify Parent of Completion]
    N --> W[Log Rejection Reason]
    O --> X[Parent Provides Info]
    X --> K
```

### 5. Role-Based Access Control Matrix

```mermaid
graph LR
    subgraph "System Roles"
        A[👨‍💼 Admin<br/>Full Access]
        B[👩‍💼 Manager<br/>Management]
        C[👩‍⚕️ Nurse<br/>Medical Ops]
        D[👨‍👩‍👧‍👦 Parent<br/>Limited Access]
        E[👤 User<br/>Basic Access]
    end
    
    subgraph "System Functions"
        F[User Management]
        G[Student Management]
        H[Medical Operations]
        I[Health Profiles]
        J[Reporting]
        K[System Config]
        L[Communication]
    end
    
    A --> F
    A --> G
    A --> H
    A --> I
    A --> J
    A --> K
    A --> L
    
    B --> G
    B --> H
    B --> I
    B --> J
    B --> L
    
    C --> H
    C --> I
    C --> L
    
    D --> I
    D --> L
    
    E --> L
    
    style A fill:#ff6b6b
    style B fill:#4ecdc4
    style C fill:#45b7d1
    style D fill:#96ceb4
    style E fill:#ffeaa7
```

## 📱 User Interface Wireframes

### 1. Dashboard Layout Structure

```
┌─────────────────────────────────────────────────────────────┐
│                    HEADER NAVIGATION                        │
├─────────────────────────────────────────────────────────────┤
│ 🏥 SMMS Logo    │  Navigation Menu  │  User Profile  │ 🔔   │
└─────────────────────────────────────────────────────────────┘
┌─────────────────┬───────────────────────────────────────────┐
│                 │                                           │
│   SIDEBAR       │              MAIN CONTENT                 │
│   NAVIGATION    │                                           │
│                 │  ┌─────────────────────────────────────┐  │
│ 📊 Dashboard    │  │                                     │  │
│ 👥 Users        │  │         DASHBOARD WIDGETS           │  │
│ 🎓 Students     │  │                                     │  │
│ 🏥 Medical      │  │  ┌─────────┐  ┌─────────┐  ┌─────┐  │  │
│ 📊 Health       │  │  │ Today's │  │ Medical │  │Stats│  │  │
│ 🎯 Activities   │  │  │ Schedule│  │ Alerts  │  │ Box │  │  │
│ 💬 Messages     │  │  └─────────┘  └─────────┘  └─────┘  │  │
│ 📝 Reports      │  │                                     │  │
│ ⚙️ Settings     │  │  ┌─────────────────────────────────┐  │  │
│                 │  │  │                                 │  │
│                 │  │  │      RECENT ACTIVITIES          │  │
│                 │  │  │                                 │  │
│                 │  │  └─────────────────────────────────┘  │  │
│                 │  └─────────────────────────────────────┘  │
└─────────────────┴───────────────────────────────────────────┘
```

### 2. Medical Request Form Layout

```
┌─────────────────────────────────────────────────────────────┐
│                 CREATE MEDICAL REQUEST                      │
├─────────────────────────────────────────────────────────────┤
│                                                            │
│  Student Information                                       │
│  ┌─────────────────┐  ┌─────────────────┐                │
│  │ Student Name    │  │ Student Code    │                │
│  └─────────────────┘  └─────────────────┘                │
│  ┌─────────────────┐  ┌─────────────────┐                │
│  │ Class           │  │ Parent Contact  │                │
│  └─────────────────┘  └─────────────────┘                │
│                                                            │
│  Medication Details                                        │
│  ┌─────────────────────────────────────────────────────┐  │
│  │ Medication Name                                     │  │
│  └─────────────────────────────────────────────────────┘  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌───────────┐  │
│  │ Dosage          │  │ Unit            │  │ Frequency │  │
│  └─────────────────┘  └─────────────────┘  └───────────┘  │
│                                                            │
│  Schedule Information                                      │
│  ┌─────────────────┐  ┌─────────────────┐                │
│  │ Start Date      │  │ End Date        │                │
│  └─────────────────┘  └─────────────────┘                │
│  ┌─────────────────────────────────────────────────────┐  │
│  │ Administration Times (JSON)                         │  │
│  │ ["08:00", "12:00", "18:00"]                        │  │
│  └─────────────────────────────────────────────────────┘  │
│                                                            │
│  Additional Information                                    │
│  ┌─────────────────────────────────────────────────────┐  │
│  │ Special Instructions / Notes                        │  │
│  │                                                     │  │
│  │                                                     │  │
│  └─────────────────────────────────────────────────────┘  │
│                                                            │
│  ┌─────────────────┐  ┌─────────────────┐                │
│  │ 📎 Attach Files │  │ 💾 Save Request │                │
│  └─────────────────┘  └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 Data Flow Diagrams

### 1. Student Health Profile Update Flow

```mermaid
graph TD
    A[Nurse Opens Student Profile] --> B[Load Current Health Data]
    B --> C[Display Health Profile Form]
    C --> D[Nurse Updates Information]
    D --> E{Validation Check}
    E -->|Valid| F[Save to Database]
    E -->|Invalid| G[Show Validation Errors]
    G --> C
    F --> H[Update Audit Trail]
    H --> I[Generate Change Notification]
    I --> J[Notify Parent if Significant Change]
    J --> K[Update Health Trends]
    K --> L[Refresh Dashboard Statistics]
```

### 2. Emergency Notification Flow

```mermaid
graph LR
    A[Medical Incident Occurs] --> B[Staff Creates Incident Report]
    B --> C{Severity Level}
    C -->|Critical| D[Immediate Parent Notification]
    C -->|High| E[Priority Parent Notification]
    C -->|Medium| F[Standard Parent Notification]
    C -->|Low| G[Daily Summary Notification]
    
    D --> H[SMS + Phone Call]
    E --> I[SMS + Email]
    F --> J[Email + In-App]
    G --> K[In-App Only]
    
    H --> L[Emergency Contact Protocol]
    I --> M[Follow-up Required]
    J --> N[Standard Follow-up]
    K --> O[No Immediate Action]
```
