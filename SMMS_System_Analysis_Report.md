# 🏥 School Medical Management System (SMMS) - Comprehensive System Analysis Report

**Project Name:** School Medical Management System (SMMS)
**Version:** 1.0
**Date:** December 2024
**Prepared by:** System Analysis Team
**Document Type:** Technical Analysis & Requirements Specification

---

## 📋 Executive Summary

The School Medical Management System (SMMS) is a comprehensive web-based application designed to streamline medical operations in educational institutions. The system provides end-to-end management of student health records, medical inventory, medication administration, and communication between school medical staff and parents.

**Key System Highlights:**
- **Multi-role Architecture**: Supports Admin, Manager, Nurse, Parent, and User roles
- **Comprehensive Health Tracking**: Complete student health profiles with historical data
- **Real-time Communication**: Instant notifications and alerts for medical situations
- **Secure Authentication**: Multi-method authentication including OTP and Google OAuth
- **Modern Technology Stack**: Built with .NET 8, React 18, and modern web technologies

---

## 📑 Table of Contents
1. [Overview](#i-overview)
2. [User Requirements](#1-user-requirements)
3. [Overall Functionalities](#2-overall-functionalities)
4. [System High Level Design](#3-system-high-level-design)
5. [Requirement Specifications](#ii-requirement-specifications)
6. [Design Specifications](#iii-design-specifications)
7. [Appendix](#iv-appendix)

---

## I. Overview

### 🎯 Project Objectives

The SMMS system aims to achieve the following primary objectives:

1. **Digitize Health Records**: Transform paper-based health records into a comprehensive digital system
2. **Streamline Medical Operations**: Automate medication scheduling, inventory management, and incident reporting
3. **Enhance Communication**: Provide real-time communication channels between medical staff and parents
4. **Ensure Compliance**: Maintain detailed audit trails and comply with health data regulations
5. **Improve Efficiency**: Reduce administrative overhead and improve response times for medical situations

### 🏗️ System Architecture Overview

The SMMS follows a modern, scalable architecture pattern:

```
┌─────────────────────────────────────────────────────────────┐
│                    PRESENTATION LAYER                        │
├─────────────────────────────────────────────────────────────┤
│  React Frontend (TypeScript)    │    .NET Web API            │
│  - Role-based UI Components     │    - RESTful Endpoints     │
│  - Real-time Notifications      │    - JWT Authentication    │
│  - Responsive Design            │    - Input Validation      │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   APPLICATION LAYER                         │
├─────────────────────────────────────────────────────────────┤
│  Business Logic Services        │    External Integrations   │
│  - Medical Management          │    - Firebase (SMS/OTP)    │
│  - User Management             │    - Cloudinary (Images)   │
│  - Health Profile Management   │    - Google OAuth          │
│  - Notification Services       │    - Email Services        │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    DOMAIN LAYER                             │
├─────────────────────────────────────────────────────────────┤
│  Domain Entities & Business Rules                           │
│  - User, Student, MedicalRequest, HealthProfile            │
│  - Business Logic & Validation Rules                       │
│  - Domain Events & Specifications                          │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                 INFRASTRUCTURE LAYER                        │
├─────────────────────────────────────────────────────────────┤
│  Data Access & External Services                           │
│  - Entity Framework Core       │    - SignalR Hubs         │
│  - SQL Server Database         │    - Redis Cache          │
│  - Repository Pattern          │    - File Storage         │
└─────────────────────────────────────────────────────────────┘
```

### 1. User Requirements

#### 1.1 System Actors & Stakeholders

The SMMS system serves multiple stakeholders with distinct roles and responsibilities:

| Actor | Description | Primary Responsibilities | Access Level | User Count (Est.) |
|-------|-------------|-------------------------|--------------|-------------------|
| **👨‍💼 Admin** | System administrator with full access | User management, system configuration, data oversight, security management | Full system access | 1-2 per school |
| **👩‍💼 Manager** | School management staff | Student oversight, staff coordination, reporting, policy enforcement | High-level management functions | 2-5 per school |
| **👩‍⚕️ Nurse** | School medical staff | Medical operations, health assessments, medication administration, emergency response | Medical operations and student care | 3-10 per school |
| **👨‍👩‍👧‍👦 Parent** | Student guardians | View children's health records, receive notifications, provide consent, communicate with staff | Limited access to own children's records | 500-2000 per school |
| **👤 User** | General system user | Basic profile management, information access | Basic access level | Variable |

#### 1.2 Detailed Actor Analysis

**🔐 Admin Role - System Administrator**
- **Primary Goals**: Maintain system integrity, manage users, ensure data security
- **Key Activities**:
  - User account creation and management
  - System configuration and settings
  - Data backup and recovery operations
  - Security monitoring and audit reviews
  - Integration with external systems
- **Technical Requirements**: Advanced technical knowledge, security clearance
- **Success Metrics**: System uptime >99%, zero security breaches, efficient user onboarding

**📊 Manager Role - School Management**
- **Primary Goals**: Oversee school health operations, ensure compliance, manage resources
- **Key Activities**:
  - Student enrollment and class management
  - Staff performance monitoring
  - Health campaign planning and execution
  - Compliance reporting and documentation
  - Budget planning for medical supplies
- **Technical Requirements**: Basic to intermediate computer skills
- **Success Metrics**: Improved operational efficiency, regulatory compliance, cost optimization

**🏥 Nurse Role - Medical Staff**
- **Primary Goals**: Provide quality healthcare, maintain accurate records, ensure student safety
- **Key Activities**:
  - Daily medication administration
  - Health assessments and screenings
  - Medical incident response and documentation
  - Parent communication regarding health issues
  - Medical inventory management
- **Technical Requirements**: Medical training, basic computer literacy
- **Success Metrics**: Reduced medication errors, faster emergency response, improved health outcomes

**👪 Parent Role - Student Guardians**
- **Primary Goals**: Monitor children's health, stay informed, provide necessary consents
- **Key Activities**:
  - View children's health profiles and medical history
  - Receive real-time notifications about health incidents
  - Provide consent for medical treatments and activities
  - Communicate with school medical staff
  - Update emergency contact information
- **Technical Requirements**: Basic smartphone/computer usage
- **Success Metrics**: Increased engagement, faster response to notifications, improved satisfaction

#### 1.3 Comprehensive Use Case Analysis

Based on detailed codebase analysis, the system supports 25+ primary use cases organized into functional modules:

**🔐 Authentication & Security Module**
- **UC-001**: Multi-Method User Authentication (Email/Password, Phone/OTP, Google OAuth)
- **UC-002**: Secure Password Management and Reset
- **UC-003**: Role-Based Access Control and Authorization
- **UC-004**: Session Management and Security Monitoring
- **UC-005**: User Profile Management and Settings

**👥 User & Student Management Module**
- **UC-006**: Comprehensive User Account Management (Admin only)
- **UC-007**: Student Registration and Enrollment
- **UC-008**: Student Information Management and Updates
- **UC-009**: Class Assignment and Academic Organization
- **UC-010**: Parent-Student Relationship Management
- **UC-011**: Bulk Student Data Import/Export

**🏥 Medical Operations Module**
- **UC-012**: Medical Stock Inventory Management
- **UC-013**: Medication Request Creation and Processing
- **UC-014**: Daily Medication Schedule Management
- **UC-015**: Medication Administration Recording
- **UC-016**: Medical Incident Reporting and Tracking
- **UC-017**: Emergency Response Procedures
- **UC-018**: Medical Equipment Tracking

**📊 Health Profile & Records Module**
- **UC-019**: Comprehensive Health Profile Management
- **UC-020**: Health Assessment and Screening Records
- **UC-021**: Vaccination History and Schedule Tracking
- **UC-022**: Growth and Development Monitoring
- **UC-023**: Chronic Condition Management
- **UC-024**: Health Trend Analysis and Reporting

**🎯 Health Activities & Campaigns Module**
- **UC-025**: Health Campaign Planning and Execution
- **UC-026**: Vaccination Campaign Management
- **UC-027**: Health Screening Program Coordination
- **UC-028**: Activity Consent Collection and Management
- **UC-029**: Campaign Effectiveness Tracking

**💬 Communication & Notification Module**
- **UC-030**: Real-time Notification System
- **UC-031**: Parent-Staff Communication Platform
- **UC-032**: Emergency Alert Broadcasting
- **UC-033**: Counseling Schedule Management
- **UC-034**: Blog and News Management

**📈 Reporting & Analytics Module**
- **UC-035**: Health Statistics and Dashboards
- **UC-036**: Medical Inventory Reports
- **UC-037**: Compliance and Audit Reports
- **UC-038**: Student Health Summaries
- **UC-039**: System Usage Analytics

#### 1.4 Use Case Priority Matrix

| Priority Level | Use Cases | Business Impact | Implementation Complexity |
|----------------|-----------|-----------------|---------------------------|
| **Critical** | UC-001, UC-012, UC-013, UC-016, UC-030 | High | Medium |
| **High** | UC-007, UC-019, UC-025, UC-031, UC-035 | High | Medium-High |
| **Medium** | UC-006, UC-020, UC-026, UC-033, UC-036 | Medium | Medium |
| **Low** | UC-011, UC-024, UC-037, UC-038, UC-039 | Medium | Low-Medium |

#### 1.5 User Journey Mapping

**👩‍⚕️ Nurse Daily Workflow:**
```
Login → Dashboard → Check Daily Medication Schedule →
Administer Medications → Record Administration →
Handle Medical Incidents → Update Health Profiles →
Communicate with Parents → End of Day Summary
```

**👨‍👩‍👧‍👦 Parent Interaction Flow:**
```
Login/OTP → View Children's Profiles → Check Notifications →
Review Medical Requests → Provide Consents →
Communicate with Staff → Update Contact Information
```

**👨‍💼 Admin Management Flow:**
```
Login → System Dashboard → User Management →
System Configuration → Monitor Activities →
Generate Reports → Security Review → Backup Operations
```

### 2. Overall Functionalities

#### 2.1 System Navigation & Screen Flow Architecture

The SMMS implements a sophisticated role-based navigation system with adaptive interfaces:

**🔄 Complete System Flow Diagram:**
```
┌─────────────────────────────────────────────────────────────┐
│                    AUTHENTICATION LAYER                     │
├─────────────────────────────────────────────────────────────┤
│  Login Options:                                            │
│  ├── Email/Password Authentication                         │
│  ├── Phone/OTP Authentication (Parents)                    │
│  ├── Google OAuth Integration                              │
│  └── Password Recovery System                              │
└─────────────────────────────────────────────────────────────┘
                              ↓
┌─────────────────────────────────────────────────────────────┐
│                   ROLE-BASED DASHBOARDS                    │
├─────────────────────────────────────────────────────────────┤
│                                                            │
│  👨‍💼 ADMIN DASHBOARD                                        │
│  ├── 📊 System Overview & Analytics                        │
│  ├── 👥 User Management (CRUD Operations)                  │
│  ├── 🎓 Student Management (Full Access)                   │
│  ├── 🏥 Medical Management (Oversight)                     │
│  ├── 📝 Blog & News Management                             │
│  ├── ⚙️ System Configuration                               │
│  ├── 🔒 Security & Audit Logs                             │
│  └── 📈 Comprehensive Reporting                            │
│                                                            │
│  👩‍💼 MANAGER DASHBOARD                                      │
│  ├── 📊 Management Overview                                │
│  ├── 🎓 Student Management (Limited)                       │
│  ├── 🏥 Health Activities Coordination                     │
│  ├── 📅 Campaign Planning & Execution                      │
│  ├── 👥 Staff Performance Monitoring                       │
│  ├── 📈 Operational Reports & Analytics                    │
│  ├── 💰 Budget & Resource Planning                         │
│  └── 📋 Compliance Management                              │
│                                                            │
│  👩‍⚕️ NURSE DASHBOARD                                        │
│  ├── 🏥 Medical Operations Center                          │
│  ├── 💊 Daily Medication Schedule                          │
│  ├── 📋 Medical Requests Queue                             │
│  ├── 🚨 Medical Incidents Management                       │
│  ├── 📊 Student Health Profiles                            │
│  ├── 💉 Vaccination Records                                │
│  ├── 📦 Medical Stock Management                           │
│  ├── 📞 Parent Communication Hub                           │
│  └── 📝 Health Assessment Tools                            │
│                                                            │
│  👨‍👩‍👧‍👦 PARENT DASHBOARD                                    │
│  ├── 👶 Children's Health Profiles                         │
│  ├── 💊 Medical Requests & History                         │
│  ├── 🔔 Real-time Notifications                            │
│  ├── 📅 Counseling Schedules                               │
│  ├── ✅ Consent Management                                  │
│  ├── 💬 Communication with Staff                           │
│  ├── 📱 Emergency Contact Updates                          │
│  └── 📊 Health Progress Tracking                           │
└─────────────────────────────────────────────────────────────┘
```

#### 2.2 Detailed Screen Specifications

**🔐 Authentication Screens (5 screens)**

1. **Login Screen (`/login`)**
   - Email/password input fields with validation
   - "Remember me" functionality
   - Links to phone login and password recovery
   - Google OAuth integration button
   - Responsive design for mobile devices

2. **Phone Login Screen (`/login-phone`)**
   - Phone number input with country code
   - OTP request and verification
   - Resend OTP functionality with cooldown
   - Automatic redirection after successful verification

3. **Forgot Password Screen (`/forgot-password`)**
   - Email input for password reset
   - OTP verification process
   - Security questions (if implemented)
   - Clear instructions and error handling

4. **OTP Confirmation Screen (`/confirm-otp`)**
   - 6-digit OTP input with auto-focus
   - Timer countdown for OTP expiration
   - Resend functionality
   - Visual feedback for success/error states

5. **Password Reset Screen (`/reset-password`)**
   - New password input with strength indicator
   - Password confirmation field
   - Security requirements display
   - Success confirmation and auto-redirect

**📊 Dashboard Screens (4 role-specific dashboards)**

1. **Admin Dashboard**
   - System health indicators and alerts
   - User activity statistics
   - Quick access to critical functions
   - Recent system events and logs
   - Performance metrics and charts

2. **Manager Dashboard**
   - School overview statistics
   - Staff performance indicators
   - Budget and resource utilization
   - Upcoming campaigns and activities
   - Compliance status indicators

3. **Nurse Dashboard**
   - Today's medication schedule
   - Pending medical requests
   - Recent medical incidents
   - Student health alerts
   - Quick action buttons for common tasks

4. **Parent Dashboard**
   - Children's health summary cards
   - Recent notifications and alerts
   - Upcoming appointments and schedules
   - Quick access to communication tools
   - Health progress visualizations

**🏥 Medical Management Screens (15+ screens)**

1. **Medical Stock Management (`/medical/stock`)**
   - Inventory overview with search and filters
   - Stock level indicators (Available, Low Stock, Out of Stock, Expired)
   - Add/Edit/Delete stock items
   - Batch tracking and expiration monitoring
   - Automated reorder alerts

2. **Medical Request Management (`/medical/requests`)**
   - Request queue with priority indicators
   - Detailed request forms with student information
   - Approval workflow with digital signatures
   - Medication scheduling interface
   - Parent notification system

3. **Daily Medication Schedule (`/medical/schedule`)**
   - Time-based medication calendar
   - Student-specific medication lists
   - Administration recording interface
   - Missed dose tracking and alerts
   - Real-time status updates

4. **Medical Incident Reporting (`/medical/incidents`)**
   - Incident creation form with severity levels
   - Photo and document attachment capabilities
   - Automatic parent notification triggers
   - Follow-up scheduling and tracking
   - Incident analysis and reporting

**🎓 Student Management Screens (10+ screens)**

1. **Student Directory (`/students`)**
   - Comprehensive student listing with search
   - Class-based filtering and organization
   - Quick access to health profiles
   - Bulk operations for data management
   - Export capabilities for reporting

2. **Student Profile Management (`/students/profile/:id`)**
   - Complete student information forms
   - Health profile integration
   - Parent contact management
   - Academic and medical history
   - Photo and document management

3. **Class Management (`/classes`)**
   - Class creation and organization
   - Student assignment and transfers
   - Capacity management and monitoring
   - Teacher and room assignments
   - Academic year management

**👥 User Management Screens (8+ screens)**

1. **User Administration (`/users`)**
   - User account creation and management
   - Role assignment and permissions
   - Account activation and deactivation
   - Password reset and security management
   - Audit trail and activity monitoring

2. **Profile Management (`/profile`)**
   - Personal information updates
   - Password change functionality
   - Notification preferences
   - Security settings and two-factor authentication
   - Activity history and session management

**📱 Communication Screens (6+ screens)**

1. **Notification Center (`/notifications`)**
   - Real-time notification feed
   - Notification categorization and filtering
   - Read/unread status management
   - Notification preferences and settings
   - Archive and search functionality

2. **Counseling Schedules (`/counseling`)**
   - Appointment scheduling interface
   - Calendar view with availability
   - Automatic reminder system
   - Video call integration (if applicable)
   - Session notes and follow-up tracking

#### 2.3 Advanced Screen Features

**🔍 Search and Filtering Capabilities:**
- Global search across all entities
- Advanced filtering with multiple criteria
- Saved search preferences
- Real-time search suggestions
- Export filtered results

**📊 Data Visualization Components:**
- Interactive charts and graphs
- Health trend visualizations
- Statistical dashboards
- Progress tracking indicators
- Comparative analysis tools

**📱 Mobile Responsiveness:**
- Adaptive layouts for all screen sizes
- Touch-friendly interface elements
- Optimized navigation for mobile devices
- Offline capability for critical functions
- Progressive Web App (PWA) features

**♿ Accessibility Features:**
- Screen reader compatibility
- Keyboard navigation support
- High contrast mode options
- Font size adjustment capabilities
- Multi-language support (future enhancement)

#### 2.4 Comprehensive Screen Authorization Matrix

The system implements sophisticated role-based access control (RBAC) with granular permissions:

| Screen/Feature | 👨‍💼 Admin | 👩‍💼 Manager | 👩‍⚕️ Nurse | 👨‍👩‍👧‍👦 Parent | 👤 User | Permission Level |
|----------------|-----------|------------|-----------|------------|---------|------------------|
| **Authentication & Profile** |
| Login/Logout | ✓ | ✓ | ✓ | ✓ | ✓ | Public |
| Profile Management | ✓ | ✓ | ✓ | ✓ | ✓ | Own Profile Only |
| Password Reset | ✓ | ✓ | ✓ | ✓ | ✓ | Own Account Only |
| **User Management** |
| User CRUD Operations | ✓ | ✗ | ✗ | ✗ | ✗ | Admin Only |
| User Role Assignment | ✓ | ✗ | ✗ | ✗ | ✗ | Admin Only |
| User Activity Monitoring | ✓ | ✗ | ✗ | ✗ | ✗ | Admin Only |
| **Student Management** |
| Student Registration | ✓ | ✓ | ✗ | ✗ | ✗ | Admin/Manager |
| Student Information Update | ✓ | ✓ | ✓ | ✗ | ✗ | Admin/Manager/Nurse |
| Student Directory View | ✓ | ✓ | ✓ | ✗ | ✗ | Staff Only |
| Own Children View | ✗ | ✗ | ✗ | ✓ | ✗ | Parent Only |
| **Medical Operations** |
| Medical Stock Management | ✓ | ✓ | ✓ | ✗ | ✗ | Medical Staff |
| Medical Request Creation | ✓ | ✓ | ✓ | ✓ | ✗ | All Authenticated |
| Medical Request Approval | ✓ | ✓ | ✓ | ✗ | ✗ | Medical Staff |
| Medication Administration | ✓ | ✓ | ✓ | ✗ | ✗ | Medical Staff |
| Medical Incident Reporting | ✓ | ✓ | ✓ | ✗ | ✗ | Medical Staff |
| **Health Management** |
| Health Profile Creation | ✓ | ✓ | ✓ | ✗ | ✗ | Medical Staff |
| Health Profile Updates | ✓ | ✓ | ✓ | ✗ | ✗ | Medical Staff |
| Health Profile Viewing | ✓ | ✓ | ✓ | ✓ | ✗ | Staff + Own Children |
| Vaccination Records | ✓ | ✓ | ✓ | ✓ | ✗ | Staff + Own Children |
| **Health Activities** |
| Campaign Creation | ✓ | ✓ | ✗ | ✗ | ✗ | Admin/Manager |
| Campaign Management | ✓ | ✓ | ✓ | ✗ | ✗ | Medical Staff |
| Consent Collection | ✓ | ✓ | ✓ | ✓ | ✗ | All Authenticated |
| **Communication** |
| Notification Sending | ✓ | ✓ | ✓ | ✗ | ✗ | Staff Only |
| Notification Receiving | ✓ | ✓ | ✓ | ✓ | ✓ | All Users |
| Counseling Scheduling | ✓ | ✓ | ✓ | ✓ | ✗ | All Authenticated |
| Parent Communication | ✓ | ✓ | ✓ | ✓ | ✗ | All Authenticated |
| **Content Management** |
| Blog Creation/Editing | ✓ | ✗ | ✗ | ✗ | ✗ | Admin Only |
| Blog Viewing | ✓ | ✓ | ✓ | ✓ | ✓ | Public |
| News Management | ✓ | ✗ | ✗ | ✗ | ✗ | Admin Only |
| **Reporting & Analytics** |
| System Reports | ✓ | ✓ | ✗ | ✗ | ✗ | Admin/Manager |
| Medical Reports | ✓ | ✓ | ✓ | ✗ | ✗ | Medical Staff |
| Student Health Reports | ✓ | ✓ | ✓ | ✓ | ✗ | Staff + Own Children |
| **System Administration** |
| System Configuration | ✓ | ✗ | ✗ | ✗ | ✗ | Admin Only |
| Backup Management | ✓ | ✗ | ✗ | ✗ | ✗ | Admin Only |
| Security Monitoring | ✓ | ✗ | ✗ | ✗ | ✗ | Admin Only |
| Audit Log Access | ✓ | ✗ | ✗ | ✗ | ✗ | Admin Only |

**🔒 Permission Levels Explained:**
- **Public**: Accessible without authentication
- **Own Profile Only**: Users can only access/modify their own data
- **Own Children**: Parents can only access their children's data
- **Staff Only**: Requires staff-level authentication
- **Medical Staff**: Requires medical staff role (Admin/Manager/Nurse)
- **Admin Only**: Requires administrator privileges

**🛡️ Security Implementation:**
- JWT token-based authentication with role claims
- Route-level authorization guards
- API endpoint permission validation
- Data filtering based on user context
- Audit logging for all sensitive operations

#### 2.5 Advanced Non-UI Functions & Background Services

**🔧 Core System Services:**

1. **Authentication & Security Services**
   - **JWT Token Management**: Automatic token generation, validation, and refresh
   - **Multi-Factor Authentication**: OTP generation and verification via Firebase
   - **Session Management**: Concurrent session tracking and security monitoring
   - **Password Security**: BCrypt hashing with configurable salt rounds
   - **OAuth Integration**: Google OAuth 2.0 implementation with secure token exchange

2. **Communication & Notification Services**
   - **Real-time SignalR Hub**: Instant notifications and live updates
   - **Email Service**: SMTP integration for detailed notifications and reports
   - **SMS Service**: Firebase integration for OTP and emergency alerts
   - **Push Notifications**: Browser-based push notifications for critical alerts
   - **Notification Queuing**: Reliable message delivery with retry mechanisms

3. **Data Management Services**
   - **Redis Caching**: High-performance caching for frequently accessed data
   - **File Storage**: Cloudinary integration for secure image and document storage
   - **Data Import/Export**: Bulk operations for student and medical data
   - **Backup Services**: Automated database backups with configurable schedules
   - **Data Synchronization**: Real-time data sync across multiple user sessions

4. **Medical Operation Services**
   - **Medication Scheduling**: Automated daily medication schedule generation
   - **Inventory Tracking**: Real-time stock level monitoring with automatic alerts
   - **Health Monitoring**: Automated health trend analysis and anomaly detection
   - **Compliance Checking**: Automated validation against medical protocols
   - **Emergency Response**: Automated emergency contact and notification systems

**⚙️ Background Processing Jobs:**

1. **Scheduled Tasks**
   - **Daily Medication Reminders**: Automated notifications for upcoming medications
   - **Stock Level Monitoring**: Regular inventory checks with low-stock alerts
   - **Health Campaign Reminders**: Automated reminders for vaccination and health activities
   - **Data Cleanup**: Automated cleanup of temporary files and expired sessions
   - **Report Generation**: Scheduled generation of daily, weekly, and monthly reports

2. **Event-Driven Processes**
   - **Medical Incident Alerts**: Immediate notifications for medical emergencies
   - **Parent Notifications**: Real-time alerts for medication administration and health updates
   - **System Health Monitoring**: Continuous monitoring of system performance and availability
   - **Security Monitoring**: Real-time detection of suspicious activities and security threats
   - **Data Validation**: Continuous validation of data integrity and consistency

**🔄 Integration Services:**

1. **External API Integrations**
   - **Firebase Admin SDK**: Complete integration for authentication and messaging
   - **Google APIs**: OAuth authentication and potential future integrations
   - **Cloudinary API**: Image upload, transformation, and delivery services
   - **Email Service Providers**: SMTP integration with multiple provider support
   - **SMS Gateways**: Multiple SMS provider integration for reliability

2. **Internal Service Communication**
   - **Service Bus Architecture**: Decoupled communication between system components
   - **Event Sourcing**: Complete audit trail of all system events and changes
   - **CQRS Implementation**: Separate read and write operations for optimal performance
   - **Microservice Communication**: RESTful APIs for inter-service communication
   - **Database Connection Pooling**: Optimized database connections for high performance

**📊 Analytics & Monitoring Services:**

1. **Performance Monitoring**
   - **Application Performance Monitoring (APM)**: Real-time performance metrics
   - **Database Performance Tracking**: Query optimization and performance analysis
   - **User Activity Analytics**: Detailed tracking of user interactions and system usage
   - **Error Tracking**: Comprehensive error logging and analysis
   - **Resource Utilization Monitoring**: CPU, memory, and storage usage tracking

2. **Business Intelligence**
   - **Health Statistics**: Automated calculation of health trends and statistics
   - **Usage Analytics**: System usage patterns and user behavior analysis
   - **Compliance Reporting**: Automated generation of regulatory compliance reports
   - **Predictive Analytics**: Early warning systems for health issues and system problems
   - **Custom Dashboards**: Configurable dashboards for different stakeholder needs

### 3. System High Level Design

#### 3.1 Comprehensive Database Design Architecture

The SMMS database implements a sophisticated relational model using Entity Framework Core with SQL Server, designed for scalability, data integrity, and optimal performance.

**🗄️ Database Architecture Overview:**

```
┌─────────────────────────────────────────────────────────────┐
│                    DATABASE ARCHITECTURE                    │
├─────────────────────────────────────────────────────────────┤
│                                                            │
│  📊 CORE ENTITIES (20+ Tables)                            │
│  ├── User Management: User, Role                          │
│  ├── Academic: Student, SchoolClass                       │
│  ├── Medical: MedicalRequest, MedicalStock, MedicalUsage  │
│  ├── Health: HealthProfile, HealthCheckupRecord           │
│  ├── Activities: HealthActivity, VaccinationCampaign      │
│  ├── Communication: Notification, ConselingSchedule       │
│  └── Content: Blog, ActivityConsent                       │
│                                                            │
│  🔗 RELATIONSHIP TYPES                                     │
│  ├── One-to-Many: User→Student, Student→HealthProfile     │
│  ├── Many-to-One: Student→SchoolClass, User→Role          │
│  ├── Many-to-Many: HealthActivity↔SchoolClass             │
│  └── Self-Referencing: User→User (Hierarchical)           │
│                                                            │
│  🛡️ DATA INTEGRITY FEATURES                               │
│  ├── Soft Delete Implementation (BaseEntity)              │
│  ├── Audit Trail (Created/Updated/Deleted tracking)       │
│  ├── Referential Integrity (Foreign Key Constraints)      │
│  ├── Data Validation (Entity Framework Validations)       │
│  └── Computed Columns (StudentCode generation)            │
└─────────────────────────────────────────────────────────────┘
```

**📋 Complete Entity Catalog:**

| Category | Entity Name | Primary Purpose | Record Count (Est.) | Relationships |
|----------|-------------|-----------------|-------------------|---------------|
| **👥 User Management** |
| User | System user accounts | 500-2000 | Role(N:1), Student(1:N), MedicalRequest(1:N) |
| Role | User roles and permissions | 5-10 | User(1:N) |
| **🎓 Academic Management** |
| Student | Student information | 1000-5000 | User(N:1), SchoolClass(N:1), HealthProfile(1:N) |
| SchoolClass | Class organization | 50-200 | Student(1:N), HealthActivity(N:M) |
| **🏥 Medical Management** |
| MedicalRequest | Medication requests | 1000-10000 | Student(N:1), User(N:1), MedicationRequestAdministration(1:N) |
| MedicalStock | Medical inventory | 100-500 | MedicalUsage(1:N) |
| MedicalUsage | Stock usage tracking | 1000-5000 | MedicalStock(N:1), User(N:1) |
| MedicationRequestAdministration | Administration records | 5000-50000 | MedicalRequest(N:1) |
| MedicalIncident | Medical incidents | 100-1000 | Student(N:1), User(N:1) |
| **📊 Health Management** |
| HealthProfile | Student health data | 1000-5000 | Student(N:1) |
| HealthCheckupRecord | Health checkup results | 2000-10000 | Student(N:1), HealthActivity(N:1) |
| VaccinationRecord | Vaccination history | 5000-25000 | Student(N:1), VaccinationCampaign(N:1) |
| **🎯 Activity Management** |
| HealthActivity | Health campaigns | 50-200 | User(N:1), HealthCheckupRecord(1:N), ActivityConsent(1:N) |
| VaccinationCampaign | Vaccination programs | 20-100 | User(N:1), VaccinationRecord(1:N), ActivityConsent(1:N) |
| ActivityConsent | Parent consents | 2000-10000 | Student(N:1), User(N:1), HealthActivity(N:1) |
| **💬 Communication** |
| Notification | System notifications | 10000-100000 | User(N:1) |
| ConselingSchedule | Counseling appointments | 500-2000 | Student(N:1), User(N:1), User(N:1) |
| **📝 Content Management** |
| Blog | News and articles | 50-500 | User(N:1) |
| **🔗 Junction Tables** |
| HealthActivityClass | Activity-Class mapping | 200-1000 | HealthActivity(N:1), SchoolClass(N:1) |
| VaccinationCampaignClass | Campaign-Class mapping | 100-500 | VaccinationCampaign(N:1), SchoolClass(N:1) |

**🏗️ Advanced Database Features:**

1. **BaseEntity Pattern Implementation**
```sql
-- All entities inherit from BaseEntity
BaseEntity {
    Id: NVARCHAR(450) PRIMARY KEY,
    CreatedTime: DATETIMEOFFSET NOT NULL,
    CreatedBy: NVARCHAR(450),
    UpdatedTime: DATETIMEOFFSET NULL,
    UpdatedBy: NVARCHAR(450) NULL,
    DeletedTime: DATETIMEOFFSET NULL,
    DeletedBy: NVARCHAR(450) NULL
}
```

2. **Computed Columns and Auto-Generation**
```sql
-- Student code auto-generation
StudentCode AS ('STD' + CAST([StudentNumber] AS VARCHAR(10))) PERSISTED,
StudentNumber INT IDENTITY(1,1) NOT NULL
```

3. **Complex Relationship Mappings**
```sql
-- Many-to-Many with additional properties
HealthActivityClass {
    Id: NVARCHAR(450) PRIMARY KEY,
    HealthActivityId: NVARCHAR(450) FOREIGN KEY,
    SchoolClassId: NVARCHAR(450) FOREIGN KEY,
    ParticipationRate: DECIMAL(5,2),
    CompletionDate: DATETIME2,
    [BaseEntity fields]
}
```

#### 3.2 Code Packages

The backend follows Clean Architecture principles with clear separation of concerns:

**SMMS.API Layer:**
- Controllers: API endpoints and request handling
- Program.cs: Application configuration and dependency injection
- Authentication & Authorization middleware

**SMMS.Application Layer:**
- Services: Business logic implementation
- DataObjects: Request/Response models
- Helpers: Utility functions and common operations

**SMMS.Domain Layer:**
- Entities: Domain models and business objects
- Enums: System enumerations
- Interfaces: Repository contracts

**SMMS.Infrastructure Layer:**
- Context: Database context and configuration
- Repositories: Data access implementation
- External Services: Third-party integrations
- Hubs: SignalR real-time communication

**Frontend Architecture:**
- React with TypeScript
- Component-based architecture
- Service layer for API communication
- Context-based state management
- Tailwind CSS for styling

---

## II. Requirement Specifications

### 1. Authentication Feature

#### 1.1 UC-2_Login System

**Description:** Multi-method authentication system supporting email/password and phone/OTP login

**Actors:** All user types (Admin, Manager, Nurse, Parent, User)

**Preconditions:**
- User must have a valid account in the system
- For phone login: Phone number must be registered and verified

**Main Flow:**

**Email/Password Login:**
1. User navigates to login page
2. User enters email and password
3. System validates credentials against database
4. System generates JWT token upon successful authentication
5. User is redirected to role-appropriate dashboard

**Phone/OTP Login:**
1. User navigates to phone login page
2. User enters phone number
3. System sends OTP via Firebase SMS service
4. User enters received OTP
5. System validates OTP and generates JWT token
6. User is redirected to dashboard

**Alternative Flows:**
- Google OAuth integration for email-based login
- Password reset flow via OTP verification
- Account creation with OTP verification

**Postconditions:**
- User is authenticated and has valid JWT token
- User session is established
- User has access to role-appropriate features

### 2. Medical Management Feature

#### 2.1 UC-5_Medical Stock Management

**Description:** Comprehensive medical inventory management system

**Actors:** Admin, Manager, Nurse

**Preconditions:**
- User must be authenticated with appropriate role
- Medical stock items must be properly categorized

**Main Flow:**
1. Authorized user accesses medical stock management
2. System displays current inventory with status indicators
3. User can perform CRUD operations on medical items
4. System tracks stock levels and expiration dates
5. System generates alerts for low stock or expired items
6. System maintains audit trail of all stock movements

**Business Rules:**
- Only authorized medical staff can modify stock
- Stock levels must be tracked in real-time
- Expired medications must be flagged and removed
- All stock movements must be logged with user and timestamp

#### 2.2 UC-6_Medical Request Processing

**Description:** End-to-end medical request management from creation to administration

**Actors:** Admin, Manager, Nurse, Parent

**Preconditions:**
- Student must be registered in the system
- Medical stock must be available for requested medications
- Parent consent may be required for certain medications

**Main Flow:**
1. Authorized user creates medical request for student
2. System validates request against student health profile
3. Request is reviewed and approved by medical staff
4. System schedules medication administration based on prescription
5. Nurse records actual medication administration
6. System updates student medical history
7. Parent receives notification of medication given

**Complex Scenarios:**
- Daily medication schedules with multiple time slots
- Recurring medication requests
- Emergency medication administration
- Medication refusal or adverse reactions

### 3. Student & Health Management Feature

#### 3.1 UC-9_Student Registration and Management

**Description:** Comprehensive student information management system

**Actors:** Admin, Manager, Nurse

**Preconditions:**
- User must have appropriate permissions
- School class structure must be established
- Parent information must be available

**Main Flow:**
1. Authorized user accesses student management
2. User creates new student record with basic information
3. System generates unique student code (STD + auto-increment number)
4. Student is assigned to appropriate school class
5. Health profile is created and linked to student
6. Parent-student relationship is established
7. System sends notification to parent about registration

**Data Management:**
- Student demographic information
- Class assignment and academic year tracking
- Health profile initialization
- Parent contact information linkage
- Photo and document management

#### 3.2 UC-10_Health Profile Management

**Description:** Detailed health information tracking for each student

**Actors:** Admin, Manager, Nurse, Parent (view only for own children)

**Preconditions:**
- Student must be registered in system
- Health assessment data must be available
- Medical history information should be collected

**Main Flow:**
1. Medical staff accesses student health profile
2. System displays comprehensive health information
3. Staff updates health metrics (BMI, vision, hearing, dental)
4. System calculates and tracks health trends
5. Abnormal findings are flagged for follow-up
6. Vaccination history is maintained and updated
7. Parent notes and concerns are recorded

**Health Metrics Tracked:**
- Physical measurements (height, weight, BMI)
- Vision and hearing assessments
- Dental health status
- Vaccination history and schedules
- Chronic conditions and allergies
- Parent concerns and notes
- Medical incident history

---

## III. Design Specifications

### 1. Database Design Specifications

#### 1.1 Entity Relationship Diagram

The SMMS database implements a comprehensive relational model with the following key entities and relationships:

**Core Entity Details:**

**User Entity:**
```sql
User {
    Id: string (PK)
    RoleId: string (FK → Role.Id)
    Email: string
    Phone: string
    Password: string (hashed)
    FullName: string
    Image: string (nullable)
    CreatedTime: DateTimeOffset
    CreatedBy: string
    UpdatedTime: DateTimeOffset (nullable)
    UpdatedBy: string (nullable)
    DeletedTime: DateTimeOffset (nullable)
    DeletedBy: string (nullable)
}
```

**Student Entity:**
```sql
Student {
    Id: string (PK)
    StudentNumber: int (Identity, Auto-increment)
    StudentCode: string (Computed: 'STD' + StudentNumber)
    ParentId: string (FK → User.Id)
    ClassId: string (FK → SchoolClass.Id)
    FullName: string
    Gender: string
    DateOfBirth: DateTime
    Image: string (nullable)
    [BaseEntity fields]
}
```

**MedicalRequest Entity:**
```sql
MedicalRequest {
    Id: string (PK)
    StudentId: string (FK → Student.Id)
    UserId: string (FK → User.Id)
    MedicationName: string
    Dosage: string
    Unit: string
    Frequency: int
    TotalQuantity: int
    RemainingQuantity: int
    TimeToAdminister: string (JSON array)
    StartDate: DateTime
    EndDate: DateTime
    Notes: string (nullable)
    Status: string
    ImageUrl: string (nullable)
    [BaseEntity fields]
}
```

**HealthProfile Entity:**
```sql
HealthProfile {
    Id: string (PK)
    StudentId: string (FK → Student.Id)
    Vision: string
    Hearing: string
    Dental: string
    BMI: double
    Weight: double
    Height: double
    AbnormalNote: string (nullable)
    VaccinationHistory: string (nullable)
    ParentNote: string (nullable)
    [BaseEntity fields]
}
```

#### 1.2 Key Relationships and Constraints

**Primary Relationships:**
1. **User → Student (1:N)**: Parent-child relationship
   - Foreign Key: Student.ParentId → User.Id
   - Constraint: Restrict deletion to maintain data integrity

2. **Student → SchoolClass (N:1)**: Class assignment
   - Foreign Key: Student.ClassId → SchoolClass.Id
   - Constraint: Restrict deletion, students must be reassigned before class deletion

3. **Student → HealthProfile (1:N)**: Health history tracking
   - Foreign Key: HealthProfile.StudentId → Student.Id
   - Constraint: Cascade deletion when student is removed

4. **Student → MedicalRequest (1:N)**: Medical prescriptions
   - Foreign Key: MedicalRequest.StudentId → Student.Id
   - Constraint: Restrict deletion to maintain medical history

5. **User → MedicalRequest (1:N)**: Request creator tracking
   - Foreign Key: MedicalRequest.UserId → User.Id
   - Constraint: Restrict deletion to maintain audit trail

**Complex Relationships:**
1. **Many-to-Many: HealthActivity ↔ SchoolClass**
   - Junction Table: HealthActivityClass
   - Enables health campaigns targeting specific classes

2. **Many-to-Many: VaccinationCampaign ↔ SchoolClass**
   - Junction Table: VaccinationCampaignClass
   - Manages vaccination programs across multiple classes

#### 1.3 Data Integrity and Business Rules

**Soft Delete Implementation:**
- All entities inherit from BaseEntity with DeletedTime field
- Repository pattern filters out soft-deleted records automatically
- Maintains data integrity while allowing logical deletion

**Audit Trail:**
- CreatedBy, CreatedTime, UpdatedBy, UpdatedTime fields on all entities
- Tracks all data modifications with user and timestamp
- Essential for medical record compliance and accountability

**Business Constraints:**
- Student codes are auto-generated and immutable
- Medical requests require valid student and medication data
- Health profiles maintain historical data for trend analysis
- User roles determine data access and modification permissions

**Data Validation Rules:**
- Email addresses must be unique across users
- Phone numbers are validated for OTP authentication
- Medical dosages and frequencies must be positive values
- Date ranges for medical requests must be logical (start < end)
- BMI calculations are automatically computed from height/weight

### 2. Backend Code Architecture

#### 2.1 Clean Architecture Implementation

The SMMS backend follows Clean Architecture principles with clear separation of concerns across four main layers:

**Layer Structure:**
```
SMMS.API (Presentation Layer)
├── Controllers/          # API endpoints and HTTP request handling
├── Program.cs           # Application startup and configuration
└── Config/              # Configuration files and settings

SMMS.Application (Application Layer)
├── Services/            # Business logic implementation
│   ├── Interfaces/      # Service contracts
│   └── Implements/      # Service implementations
├── DataObject/          # DTOs and data transfer models
│   ├── RequestObject/   # API request models
│   └── ResponseObject/  # API response models
└── Helpers/             # Utility services and common functions

SMMS.Domain (Domain Layer)
├── Entity/              # Domain entities and business objects
├── Enum/                # System enumerations
├── Interface/           # Repository contracts
└── Base/                # Base classes and common interfaces

SMMS.Infrastructure (Infrastructure Layer)
├── Context/             # Database context and configuration
├── Implements/          # Repository implementations
├── Migrations/          # Database migration files
└── Hubs/                # SignalR real-time communication
```

#### 2.2 Dependency Injection Configuration

**Service Registration in Program.cs:**
```csharp
// Database Context
builder.Services.AddDbContext<DatabaseContext>(options =>
    options.UseSqlServer(connectionString));

// Repository Pattern
builder.Services.AddScoped<IRepositoryManager, RepositoryManager>();

// Application Services
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<IMedicalService, MedicalService>();
builder.Services.AddScoped<IHealthActivityService, HealthActivityService>();
builder.Services.AddScoped<IBlogService, BlogService>();

// Infrastructure Services
builder.Services.AddScoped<IJwtTokenGenerator, JwtTokenGenerator>();
builder.Services.AddScoped<ISmsService, SmsService>();
builder.Services.AddScoped<IRedisCacheService, RedisCacheService>();
builder.Services.AddScoped<CloudinaryService>();
builder.Services.AddScoped<SendMailService>();
```

#### 2.3 Repository Pattern Implementation

**Generic Repository Base:**
```csharp
public class RepositoryBase<T> : IRepositoryBase<T> where T : class
{
    private readonly DatabaseContext _context;

    // Implements soft delete filtering
    public IQueryable<T> FindAll(bool trackChanges)
    {
        var query = _context.Set<T>()
            .Where(e => EF.Property<DateTimeOffset?>(e, "DeletedTime") == null);
        return trackChanges ? query : query.AsNoTracking();
    }

    // CRUD operations with soft delete support
    public void Create(T entity) => _context.Set<T>().Add(entity);
    public void Update(T entity) => _context.Set<T>().Update(entity);
    public void Delete(T entity) => _context.Set<T>().Remove(entity);
}
```

**Repository Manager Pattern:**
```csharp
public class RepositoryManager : IRepositoryManager
{
    private readonly DatabaseContext _context;

    // Lazy initialization of repositories
    public IUserRepository UserRepository =>
        _userRepository ??= new UserRepository(_context);
    public IStudentRepository StudentRepository =>
        _studentRepository ??= new StudentRepository(_context);
    public IMedicalRequestRepository MedicalRequestRepository =>
        _medicalRequestRepository ??= new MedicalRequestRepository(_context);

    public async Task SaveAsync() => await _context.SaveChangesAsync();
}
```

#### 2.4 Service Layer Architecture

**Business Logic Services:**

**Authentication Service:**
- JWT token generation and validation
- Multi-method authentication (email/password, phone/OTP, Google OAuth)
- Password hashing with BCrypt
- OTP generation and verification via Firebase

**Medical Service:**
- Medical stock management and inventory tracking
- Medical request processing and approval workflow
- Medication administration scheduling and recording
- Medical incident reporting and management

**User Service:**
- User account management and profile updates
- Student registration and information management
- Role-based access control implementation
- Parent-student relationship management

#### 2.5 API Controller Design

**RESTful API Design:**
```csharp
[ApiController]
[Route("api/medical")]
public class MedicalController : ControllerBase
{
    private readonly IMedicalService _medicalService;

    [HttpPost("stock")]
    [Authorize(Roles = "Admin,Manager,Nurse")]
    public async Task<IActionResult> CreateMedicalStock([FromBody] CreateMedicalStockRequest request)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var result = await _medicalService.CreateMedicalStockAsync(userId, request);
        return result ? Ok("Stock created successfully") : BadRequest("Failed to create stock");
    }

    [HttpGet("request/daily/today")]
    [Authorize(Roles = "Admin,Manager,Nurse")]
    public async Task<IActionResult> GetTodayMedicationSchedule()
    {
        var result = await _medicalService.GetDailyMedicationScheduleAsync(DateTime.Today);
        return Ok(result);
    }
}
```

**Authorization Patterns:**
- Role-based authorization using JWT claims
- Method-level security with [Authorize] attributes
- User context extraction from JWT tokens
- Fine-grained permission control per endpoint

#### 2.6 External Service Integration

**Third-Party Services:**
1. **Firebase Admin SDK**: OTP SMS services and phone authentication
2. **Cloudinary**: Image upload and management for user profiles and medical documents
3. **Redis**: Caching service for performance optimization
4. **SignalR**: Real-time notifications and communication
5. **Entity Framework Core**: ORM for database operations
6. **BCrypt.NET**: Password hashing and security

**Configuration Management:**
- Environment-specific settings in appsettings.json
- Secure credential management for external services
- CORS configuration for frontend integration
- JWT configuration with secure key management

### 3. Frontend Architecture

#### 3.1 React Application Structure

**Component Architecture:**
```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Basic UI elements (buttons, inputs, etc.)
│   ├── layout/         # Layout components (headers, sidebars)
│   ├── header/         # Navigation and header components
│   └── context/        # React context providers
├── pages/              # Page components and routing
│   ├── auth/           # Authentication pages
│   ├── dashboard/      # Dashboard components
│   ├── medical/        # Medical management pages
│   ├── student/        # Student management pages
│   └── user/           # User management pages
├── services/           # API service layer
├── types/              # TypeScript type definitions
├── utils/              # Utility functions and helpers
└── styles/             # CSS and styling files
```

#### 3.2 State Management and Data Flow

**Service Layer Pattern:**
```typescript
// API Base Configuration
async function ApiClient<T, D = unknown>({
  method,
  endpoint,
  data,
  headers = {},
  baseURL = "https://localhost:7172/api",
  requiresToken = true,
  contentType = "application/json",
}: ApiRequestConfig<D>): Promise<ApiResponse<T>> {
  const token = requiresToken ? getToken() : null;
  const defaultHeaders = {
    "Content-Type": contentType,
    Accept: "*/*",
    ...(token ? { Authorization: `Bearer ${token}` } : {}),
    ...headers,
  };

  const response = await axios({
    method, url: endpoint, baseURL, data, headers: defaultHeaders,
  });

  return { data: response.data, status: response.status, statusText: response.statusText };
}
```

**Authentication Service:**
```typescript
export async function FecthLogin(users: LoginRequest): Promise<void> {
  const response = await ApiClient<LoginResponse>({
    method: "POST",
    endpoint: "/auth/login",
    data: users,
    requiresToken: false,
  });

  if (response?.data?.token) {
    localStorage.setItem("token", response.data.token);
  } else {
    throw new Error("Email hoặc mật khẩu không đúng");
  }
}
```

#### 3.3 Routing and Navigation

**Role-Based Routing:**
```typescript
function App() {
  return (
    <Router>
      <Routes>
        {/* Public Routes */}
        <Route path="/login" element={<Login />} />
        <Route path="/login-phone" element={<LoginPhone />} />

        {/* Protected Routes */}
        <Route element={<AppLayout />}>
          <Route index element={<Dashboard />} />

          {/* Admin Only Routes */}
          <Route path="user" element={
            <PrivateRoute allowedRoles={["Admin"]}>
              <UserManager />
            </PrivateRoute>
          } />

          {/* Multi-Role Routes */}
          <Route path="student" element={
            <PrivateRoute allowedRoles={["Admin", "Manager", "Nurse"]}>
              <StudentManager />
            </PrivateRoute>
          } />
        </Route>
      </Routes>
    </Router>
  );
}
```

**Private Route Component:**
```typescript
export const PrivateRoute = ({ children, allowedRoles }: PrivateRouteProps) => {
  const userRole = getUserRoleFromToken();

  if (!isAuthenticated()) {
    return <Navigate to="/login" replace />;
  }

  if (allowedRoles && !allowedRoles.includes(userRole)) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
};
```

### 4. Authentication & Authorization System

#### 4.1 Multi-Method Authentication

**Authentication Methods Supported:**

1. **Email/Password Authentication:**
   - Traditional login with email and password
   - Password hashing using BCrypt for security
   - JWT token generation upon successful authentication
   - Role-based claims embedded in JWT tokens

2. **Phone/OTP Authentication:**
   - Firebase SMS service integration for OTP delivery
   - Secure OTP generation and validation
   - Phone number verification and registration
   - Primarily used for parent access

3. **Google OAuth Integration:**
   - Google OAuth 2.0 implementation
   - Email-based account linking
   - Seamless integration with existing user accounts
   - Automatic JWT token generation

**Authentication Flow:**
```csharp
public async Task<AuthResponse> LoginAsync(string email, string password)
{
    var user = _repositoryManager.UserRepository
        .FindByCondition(u => u.Email == email, false)
        .Include(u => u.Role)
        .FirstOrDefault();

    if (user == null || !BCrypt.Net.BCrypt.Verify(password, user.Password))
    {
        throw new Exception("Invalid credentials");
    }

    var token = _jwtTokenGenerator.GenerateToken(user);
    return new AuthResponse { Token = token, UserId = user.Id };
}
```

#### 4.2 JWT Token Management

**Token Structure:**
- **Header**: Algorithm and token type
- **Payload**: User claims including ID, email, role, and permissions
- **Signature**: HMAC SHA256 signature for token verification

**Token Configuration:**
```csharp
builder.Services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
    .AddJwtBearer(options =>
    {
        options.TokenValidationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidateAudience = true,
            ValidateLifetime = true,
            ValidateIssuerSigningKey = true,
            ValidIssuer = jwtSettings["Issuer"],
            ValidAudience = jwtSettings["Audience"],
            IssuerSigningKey = new SymmetricSecurityKey(
                Encoding.UTF8.GetBytes(jwtSettings["SecretKey"]))
        };
    });
```

**Token Claims:**
- **NameIdentifier**: User ID for database lookups
- **Email**: User email address
- **Role**: User role for authorization
- **Name**: User full name for display
- **Expiration**: Token validity period

#### 4.3 Role-Based Access Control (RBAC)

**System Roles:**

| Role | Description | Permissions |
|------|-------------|-------------|
| **Admin** | System Administrator | Full system access, user management, system configuration |
| **Manager** | School Management | Student management, staff coordination, reporting |
| **Nurse** | Medical Staff | Medical operations, health records, medication administration |
| **Parent** | Student Guardian | Limited access to own children's records |
| **User** | General User | Basic system access, profile management |

**Authorization Implementation:**
```csharp
[ApiController]
[Route("api/medical")]
public class MedicalController : ControllerBase
{
    [HttpPost("stock")]
    [Authorize(Roles = "Admin,Manager,Nurse")]
    public async Task<IActionResult> CreateMedicalStock([FromBody] CreateMedicalStockRequest request)
    {
        var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        // Implementation...
    }

    [HttpGet("request")]
    [Authorize(Roles = "Admin,Manager,Nurse,Parent")]
    public async Task<IActionResult> GetMedicalRequests()
    {
        // Role-specific data filtering applied in service layer
    }
}
```

#### 4.4 Frontend Authentication Integration

**Authentication Context:**
```typescript
// Token storage and retrieval
const getToken = (): string | null => {
  return localStorage.getItem("token");
};

const isAuthenticated = (): boolean => {
  const token = getToken();
  if (!token) return false;

  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp > Date.now() / 1000;
  } catch {
    return false;
  }
};

const getUserRoleFromToken = (): string | null => {
  const token = getToken();
  if (!token) return null;

  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.role;
  } catch {
    return null;
  }
};
```

**Protected Route Implementation:**
```typescript
export const PrivateRoute = ({ children, allowedRoles }: PrivateRouteProps) => {
  const userRole = getUserRoleFromToken();

  if (!isAuthenticated()) {
    return <Navigate to="/login" replace />;
  }

  if (allowedRoles && !allowedRoles.includes(userRole)) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
};
```

#### 4.5 Security Measures

**Password Security:**
- BCrypt hashing with salt rounds for password storage
- Password complexity requirements enforced
- Secure password reset flow with OTP verification

**Token Security:**
- JWT tokens with configurable expiration times
- Secure token storage in localStorage (frontend)
- Token validation on every API request
- Automatic token refresh mechanisms

**API Security:**
- CORS configuration for cross-origin requests
- HTTPS enforcement for all communications
- Request rate limiting and throttling
- Input validation and sanitization

**Data Protection:**
- Soft delete implementation to maintain data integrity
- Audit trails for all data modifications
- Role-based data filtering at service layer
- Sensitive data encryption where applicable

#### 4.6 Session Management

**Session Lifecycle:**
1. **Login**: JWT token generated and stored
2. **Request Authentication**: Token validated on each API call
3. **Token Refresh**: Automatic renewal before expiration
4. **Logout**: Token removal and session cleanup
5. **Timeout**: Automatic logout after inactivity period

**Multi-Device Support:**
- Concurrent sessions allowed across devices
- Device-specific token management
- Session invalidation capabilities
- Security notifications for new device logins

### 5. Core System Features

#### 5.1 Medical Management System

**Medical Stock Management:**
The system provides comprehensive inventory management for medical supplies and medications:

**Key Features:**
- Real-time stock level tracking with automatic alerts
- Expiration date monitoring and notifications
- Batch tracking for medication traceability
- Automated reorder point calculations
- Comprehensive audit trail for all stock movements

**Stock Status Indicators:**
```csharp
public enum MedicalStockStatus
{
    Available = 0,    // In stock and available
    LowStock = 1,     // Below minimum threshold
    OutOfStock = 2,   // No stock available
    Expired = 3       // Past expiration date
}
```

**Medical Request Processing:**
End-to-end medication request management from creation to administration:

**Request Workflow:**
1. **Creation**: Medical staff or parents create medication requests
2. **Validation**: System validates against student health profile and stock availability
3. **Approval**: Medical staff reviews and approves requests
4. **Scheduling**: System creates administration schedule based on prescription
5. **Administration**: Nurses record actual medication given
6. **Tracking**: System updates remaining quantities and schedules
7. **Completion**: Request marked complete when medication course finished

**Daily Medication Schedule:**
```csharp
public async Task<DailyMedicationSchedule> GetDailyMedicationScheduleAsync(DateTime date)
{
    var schedules = await _repositoryManager.MedicalRequestRepository
        .FindByCondition(mr => mr.StartDate <= date && mr.EndDate >= date && mr.Status == "Active", false)
        .Include(mr => mr.Student)
        .Select(mr => new MedicationScheduleItem
        {
            StudentName = mr.Student.FullName,
            MedicationName = mr.MedicationName,
            Dosage = mr.Dosage,
            TimeSlots = JsonSerializer.Deserialize<string[]>(mr.TimeToAdminister),
            Notes = mr.Notes
        })
        .ToListAsync();

    return new DailyMedicationSchedule { Date = date, Schedules = schedules };
}
```

#### 5.2 Student Management System

**Student Registration and Information Management:**

**Student Entity Structure:**
- **Basic Information**: Name, gender, date of birth, contact details
- **Academic Information**: Class assignment, student code generation
- **Health Information**: Linked health profiles and medical history
- **Family Information**: Parent-student relationships and emergency contacts

**Automated Student Code Generation:**
```sql
-- Student codes are automatically generated as 'STD' + auto-increment number
StudentCode: Computed Column = 'STD' + CAST([StudentNumber] AS VARCHAR(10))
```

**Class Management:**
- Hierarchical class structure with room assignments
- Student capacity tracking and enrollment limits
- Class-based health activity and vaccination campaign targeting
- Academic year and semester management

**Student Data Access Patterns:**
```csharp
public async Task<StudentResponse> GetStudentByIdAsync(string id)
{
    return await _repositoryManager.StudentRepository
        .FindByCondition(s => s.Id == id && s.DeletedTime == null, false)
        .Include(s => s.SchoolClass)
        .Include(s => s.HealthProfiles)
        .Include(s => s.HealthCheckupRecords)
        .Select(s => new StudentResponse
        {
            Id = s.Id,
            StudentCode = s.StudentCode,
            FullName = s.FullName,
            Gender = s.Gender,
            DateOfBirth = s.DateOfBirth,
            StudentClass = new SchoolClassResponse
            {
                Id = s.SchoolClass.Id,
                ClassName = s.SchoolClass.ClassName,
                ClassRoom = s.SchoolClass.ClassRoom
            },
            HealthProfile = s.HealthProfiles
                .Where(hp => hp.DeletedTime == null)
                .OrderByDescending(hp => hp.CreatedTime)
                .FirstOrDefault()
        })
        .FirstOrDefaultAsync();
}
```

#### 5.3 Health Profile Management

**Comprehensive Health Tracking:**
The system maintains detailed health profiles for each student with historical tracking:

**Health Metrics Monitored:**
- **Physical Measurements**: Height, weight, BMI calculations
- **Sensory Health**: Vision and hearing assessments
- **Dental Health**: Dental examination results and recommendations
- **Vaccination History**: Complete immunization records
- **Medical Conditions**: Chronic conditions, allergies, and special needs
- **Parent Concerns**: Parent-reported health issues and observations

**Health Profile Data Structure:**
```csharp
public class HealthProfile : BaseEntity
{
    public string StudentId { get; set; }
    public string Vision { get; set; }        // "20/20", "Needs correction", etc.
    public string Hearing { get; set; }       // "Normal", "Impaired", etc.
    public string Dental { get; set; }        // "Good", "Needs treatment", etc.
    public double BMI { get; set; }           // Calculated from height/weight
    public double Weight { get; set; }        // In kilograms
    public double Height { get; set; }        // In centimeters
    public string AbnormalNote { get; set; }  // Medical staff observations
    public string VaccinationHistory { get; set; } // Immunization records
    public string ParentNote { get; set; }    // Parent concerns and notes
}
```

**Health Trend Analysis:**
- Historical health data comparison
- Growth chart tracking for height and weight
- BMI trend analysis with health recommendations
- Vaccination schedule compliance monitoring
- Early warning system for health deterioration

#### 5.4 Medical Incident Management

**Incident Reporting and Tracking:**
Comprehensive system for managing medical incidents and emergencies:

**Incident Types:**
- Minor injuries (cuts, bruises, sprains)
- Medical emergencies (allergic reactions, seizures)
- Medication-related incidents (adverse reactions, missed doses)
- Behavioral health incidents (anxiety attacks, emotional distress)

**Incident Workflow:**
1. **Immediate Response**: Staff records incident details in real-time
2. **Medical Assessment**: Nurse evaluates severity and required treatment
3. **Parent Notification**: Automatic alerts sent to parents/guardians
4. **Treatment Documentation**: All treatments and interventions recorded
5. **Follow-up Scheduling**: Counseling or medical follow-up appointments
6. **Incident Analysis**: Review for prevention and policy improvements

**Incident Status Tracking:**
```csharp
public enum MedicalIncidentStatus
{
    Reported = 0,     // Initial incident report
    InProgress = 1,   // Being treated/managed
    Resolved = 2,     // Incident resolved
    RequiresFollowup = 3  // Needs additional care
}
```

#### 5.5 Health Activity and Vaccination Campaigns

**Health Campaign Management:**
Systematic approach to organizing school-wide health initiatives:

**Campaign Types:**
- **Vaccination Campaigns**: Mass immunization programs
- **Health Screenings**: Vision, hearing, dental checkups
- **Health Education**: Workshops and awareness programs
- **Preventive Care**: Regular health assessments

**Campaign Workflow:**
1. **Planning**: Medical staff creates campaign with target classes
2. **Consent Collection**: Parents provide consent for student participation
3. **Scheduling**: System schedules activities across multiple days/classes
4. **Execution**: Staff records participation and results
5. **Follow-up**: System tracks completion rates and schedules follow-ups
6. **Reporting**: Comprehensive reports on campaign effectiveness

**Many-to-Many Relationships:**
```csharp
// Health activities can target multiple classes
public class HealthActivityClass : BaseEntity
{
    public string HealthActivityId { get; set; }
    public string SchoolClassId { get; set; }
    public virtual HealthActivity HealthActivity { get; set; }
    public virtual SchoolClass SchoolClass { get; set; }
}

// Vaccination campaigns can cover multiple classes
public class VaccinationCampaignClass : BaseEntity
{
    public string VaccinationCampaignId { get; set; }
    public string SchoolClassId { get; set; }
    public virtual VaccinationCampaign VaccinationCampaign { get; set; }
    public virtual SchoolClass SchoolClass { get; set; }
}
```

#### 5.6 Communication and Notification System

**Multi-Channel Communication:**
The system provides comprehensive communication capabilities:

**Notification Types:**
- **Medical Alerts**: Medication reminders, health incidents
- **Administrative**: Schedule changes, policy updates
- **Emergency**: Critical health situations requiring immediate attention
- **Informational**: Health tips, campaign announcements

**Communication Channels:**
1. **In-App Notifications**: Real-time notifications within the system
2. **Email Notifications**: Detailed information and documentation
3. **SMS Alerts**: Critical and time-sensitive notifications
4. **Push Notifications**: Mobile app notifications (if applicable)

**Real-Time Communication with SignalR:**
```csharp
public class NotificationHub : Hub
{
    public async Task JoinUserGroup(string userId)
    {
        await Groups.AddToGroupAsync(Context.ConnectionId, $"User_{userId}");
    }

    public async Task SendNotificationToUser(string userId, string message)
    {
        await Clients.Group($"User_{userId}").SendAsync("ReceiveNotification", message);
    }
}
```

**Notification Targeting:**
- Role-based notifications (all nurses, all parents, etc.)
- Individual user notifications
- Class-based notifications for specific student groups
- Emergency broadcast capabilities for critical situations

### 6. Frontend Architecture and User Interface

#### 6.1 React Application Architecture

**Technology Stack:**
- **React 18**: Modern React with hooks and functional components
- **TypeScript**: Type-safe development with strong typing
- **Vite**: Fast build tool and development server
- **Tailwind CSS**: Utility-first CSS framework for styling
- **React Router**: Client-side routing and navigation
- **Axios**: HTTP client for API communication
- **Lucide React**: Modern icon library

**Project Structure:**
```
FE/src/
├── components/              # Reusable UI components
│   ├── ui/                 # Basic UI elements
│   │   ├── Button.tsx      # Reusable button component
│   │   ├── Input.tsx       # Form input components
│   │   ├── Modal.tsx       # Modal dialog component
│   │   └── Toast.tsx       # Notification toast component
│   ├── layout/             # Layout components
│   │   ├── AppLayout.tsx   # Main application layout
│   │   ├── HomeLayout.tsx  # Public pages layout
│   │   └── Sidebar.tsx     # Navigation sidebar
│   ├── header/             # Header and navigation
│   └── context/            # React context providers
├── pages/                  # Page components
│   ├── auth/               # Authentication pages
│   ├── dashboard/          # Dashboard components
│   ├── medical/            # Medical management
│   ├── student/            # Student management
│   ├── user/               # User management
│   └── blog/               # Blog and news
├── services/               # API service layer
├── types/                  # TypeScript definitions
├── utils/                  # Utility functions
└── styles/                 # Global styles
```

#### 6.2 Component Architecture and Design Patterns

**Component Hierarchy:**
```typescript
App
├── Router
│   ├── PublicRoutes
│   │   ├── Login
│   │   ├── LoginPhone
│   │   ├── ForgotPassword
│   │   └── Home
│   └── PrivateRoutes (Protected by PrivateRoute)
│       ├── AppLayout
│       │   ├── Header
│       │   ├── Sidebar
│       │   └── MainContent
│       │       ├── Dashboard (Role-specific)
│       │       ├── UserManager
│       │       ├── StudentManager
│       │       ├── MedicalManager
│       │       └── HealthProfiles
│       └── Modals & Overlays
```

**Reusable Component Design:**
```typescript
// Example: Reusable Button Component
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  onClick,
  children
}) => {
  const baseClasses = 'font-medium rounded-lg transition-colors duration-200';
  const variantClasses = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white',
    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800',
    danger: 'bg-red-600 hover:bg-red-700 text-white'
  };
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      }`}
      disabled={disabled || loading}
      onClick={onClick}
    >
      {loading ? <Spinner /> : children}
    </button>
  );
};
```

#### 6.3 State Management and Data Flow

**Service-Based Architecture:**
The application uses a service-based approach for state management rather than complex state management libraries:

**API Service Layer:**
```typescript
// Base API client with authentication
export default async function ApiClient<T, D = unknown>({
  method,
  endpoint,
  data,
  headers = {},
  baseURL = "https://localhost:7172/api",
  requiresToken = true,
  contentType = "application/json",
}: ApiRequestConfig<D>): Promise<ApiResponse<T>> {
  try {
    const token = requiresToken ? getToken() : null;
    const defaultHeaders: Record<string, string> = {
      "Content-Type": contentType,
      Accept: "*/*",
      ...(token ? { Authorization: `Bearer ${token}` } : {}),
      ...headers,
    };

    const response: AxiosResponse<T> = await axios({
      method,
      url: endpoint,
      baseURL,
      data,
      headers: defaultHeaders,
    });

    return {
      data: response.data,
      status: response.status,
      statusText: response.statusText,
    };
  } catch (error) {
    throw new Error(`API request failed: ${error.message}`);
  }
}
```

**Domain-Specific Services:**
```typescript
// Medical Service Example
export class MedicalService {
  static async getAllMedicalStock(): Promise<MedicalStock[]> {
    const response = await ApiClient<MedicalStock[]>({
      method: "GET",
      endpoint: "/medical/stock",
    });
    return response.data;
  }

  static async createMedicalRequest(request: CreateMedicalRequestRequest): Promise<boolean> {
    const response = await ApiClient<boolean>({
      method: "POST",
      endpoint: "/medical/request",
      data: request,
    });
    return response.data;
  }

  static async getDailyMedicationSchedule(date: string): Promise<DailyMedicationSchedule> {
    const response = await ApiClient<DailyMedicationSchedule>({
      method: "GET",
      endpoint: `/medical/request/daily/${date}`,
    });
    return response.data;
  }
}
```

#### 6.4 Routing and Navigation System

**Role-Based Routing Implementation:**
```typescript
function App() {
  return (
    <ToastProvider>
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route path="/unauthorized" element={<Unauthorized />} />
          <Route path="/login" element={<Login />} />
          <Route path="/login-phone" element={<LoginPhone />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />

          {/* Public Layout Routes */}
          <Route element={<HomeLayout />}>
            <Route index element={<Home />} />
            <Route path="/blog" element={<Blog />} />
            <Route path="blog/viewblog/:blogId" element={<BlogDetails />} />
          </Route>

          {/* Protected Application Routes */}
          <Route element={<AppLayout />}>
            <Route index element={<Dashboard />} />

            {/* Admin Only Routes */}
            <Route path="user" element={
              <PrivateRoute allowedRoles={["Admin"]}>
                <UserManager />
              </PrivateRoute>
            } />

            {/* Multi-Role Routes */}
            <Route path="student" element={
              <PrivateRoute allowedRoles={["Admin", "Manager", "Nurse"]}>
                <StudentManager />
              </PrivateRoute>
            } />

            {/* Medical Routes */}
            <Route path="medical" element={
              <PrivateRoute allowedRoles={["Admin", "Manager", "Nurse"]}>
                <ManagerMedical />
              </PrivateRoute>
            } />

            {/* Parent-Specific Routes */}
            <Route path="health-profile" element={
              <PrivateRoute allowedRoles={["Parent"]}>
                <HealthProfiles />
              </PrivateRoute>
            } />
          </Route>
        </Routes>
      </Router>
    </ToastProvider>
  );
}
```

**Navigation Guard Implementation:**
```typescript
export const PrivateRoute: React.FC<PrivateRouteProps> = ({
  children,
  allowedRoles
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const checkAuthentication = () => {
      const token = getToken();
      if (!token) {
        setIsAuthenticated(false);
        setLoading(false);
        return;
      }

      try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const isValid = payload.exp > Date.now() / 1000;

        setIsAuthenticated(isValid);
        setUserRole(payload.role);
      } catch (error) {
        setIsAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuthentication();
  }, []);

  if (loading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (allowedRoles && userRole && !allowedRoles.includes(userRole)) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
};
```

#### 6.5 User Interface Design and User Experience

**Design System:**
The application follows a consistent design system built with Tailwind CSS:

**Color Palette:**
- **Primary**: Blue tones for main actions and navigation
- **Secondary**: Gray tones for secondary elements
- **Success**: Green for positive actions and confirmations
- **Warning**: Yellow/Orange for alerts and warnings
- **Danger**: Red for errors and destructive actions

**Typography Scale:**
- **Headings**: Font sizes from text-sm to text-4xl
- **Body Text**: Primarily text-base with text-sm for secondary information
- **Font Weights**: Regular (400), Medium (500), Semibold (600), Bold (700)

**Component Styling Patterns:**
```typescript
// Consistent styling patterns across components
const cardStyles = "bg-white rounded-lg shadow-md border border-gray-200 p-6";
const buttonStyles = "px-4 py-2 rounded-lg font-medium transition-colors duration-200";
const inputStyles = "w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500";
const tableStyles = "min-w-full divide-y divide-gray-200 bg-white shadow-sm rounded-lg";
```

**Responsive Design:**
- Mobile-first approach with Tailwind's responsive utilities
- Breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)
- Adaptive layouts for different screen sizes
- Touch-friendly interface elements for mobile devices

**Accessibility Features:**
- Semantic HTML structure with proper heading hierarchy
- ARIA labels and descriptions for screen readers
- Keyboard navigation support
- High contrast color combinations
- Focus indicators for interactive elements

#### 6.6 Performance Optimization

**Code Splitting and Lazy Loading:**
```typescript
// Lazy loading of route components
const UserManager = lazy(() => import('@/pages/user/ManagerUser'));
const StudentManager = lazy(() => import('@/pages/student/ManagerStudent'));
const MedicalManager = lazy(() => import('@/pages/medical/ManagerMedical'));

// Suspense wrapper for lazy-loaded components
<Suspense fallback={<LoadingSpinner />}>
  <UserManager />
</Suspense>
```

**API Optimization:**
- Request caching for frequently accessed data
- Debounced search inputs to reduce API calls
- Pagination for large data sets
- Optimistic updates for better user experience

**Bundle Optimization:**
- Vite's built-in tree shaking and code splitting
- Dynamic imports for large libraries
- Image optimization and lazy loading
- CSS purging to remove unused styles

---

## IV. Appendix

### 1. Assumptions & Dependencies

#### 1.1 Technical Assumptions

**Backend Dependencies:**
- **.NET 8.0**: Modern .NET framework with long-term support
- **Entity Framework Core**: ORM for database operations with SQL Server
- **SQL Server**: Primary database management system
- **Firebase Admin SDK**: For SMS OTP services and phone authentication
- **Redis**: Caching layer for performance optimization
- **SignalR**: Real-time communication for notifications

**Frontend Dependencies:**
- **Node.js 18+**: JavaScript runtime for development and build processes
- **React 18**: Modern React framework with concurrent features
- **TypeScript 5+**: Type-safe JavaScript development
- **Vite**: Fast build tool and development server
- **Tailwind CSS**: Utility-first CSS framework

**External Service Dependencies:**
- **Cloudinary**: Image upload and management service
- **Google OAuth**: Authentication service integration
- **Firebase**: SMS and notification services
- **SMTP Server**: Email notification delivery

#### 1.2 Infrastructure Assumptions

**Development Environment:**
- Windows development environment with Visual Studio/VS Code
- SQL Server LocalDB or SQL Server Express for development
- Node.js and npm/yarn for frontend development
- Git version control system

**Production Environment:**
- Windows Server or Linux server for hosting
- SQL Server database with appropriate licensing
- HTTPS/SSL certificates for secure communication
- Load balancer for high availability (if required)

**Network and Security:**
- Secure network infrastructure with firewall protection
- HTTPS enforcement for all client-server communication
- Regular security updates and patch management
- Backup and disaster recovery procedures

### 2. Limitations & Exclusions

#### 2.1 Current System Limitations

**Scalability Limitations:**
- Single-tenant architecture (one school per deployment)
- Limited concurrent user capacity without load balancing
- File storage limitations with current Cloudinary integration
- Database performance constraints with large datasets

**Functional Limitations:**
- No multi-language support (currently Vietnamese/English only)
- Limited reporting and analytics capabilities
- No integration with external medical systems
- Basic mobile responsiveness (no native mobile app)

**Technical Limitations:**
- No offline capability for mobile users
- Limited real-time collaboration features
- Basic search functionality without advanced filtering
- No automated backup scheduling within the application

#### 2.2 Excluded Features

**Features Not Implemented:**
- Multi-school/district management
- Advanced reporting and business intelligence
- Integration with hospital management systems
- Telemedicine or video consultation features
- Advanced workflow automation
- Document management system
- Financial management and billing
- Advanced audit and compliance reporting

**Third-Party Integrations Not Included:**
- Electronic Health Record (EHR) systems
- Government health reporting systems
- Insurance claim processing
- Pharmacy management systems
- Laboratory information systems

### 3. Business Rules

#### 3.1 Medical Management Rules

**Medication Administration:**
- Only authorized medical staff (Nurse, Manager, Admin) can administer medication
- All medication administration must be recorded with timestamp and staff signature
- Parents must be notified within 30 minutes of medication administration
- Medication refusal must be documented with reason and parent notification

**Medical Stock Management:**
- Expired medications must be flagged and removed from active inventory
- Low stock alerts triggered when quantity falls below 20% of maximum capacity
- All stock movements must be logged with user identification and timestamp
- Controlled substances require additional approval and documentation

**Health Profile Management:**
- Health profiles can only be updated by authorized medical staff
- Parents can view but not modify their children's health profiles
- Abnormal health findings must trigger automatic counseling schedule creation
- Vaccination records must be verified against official documentation

#### 3.2 Access Control Rules

**Role-Based Permissions:**
- Admin: Full system access including user management and system configuration
- Manager: Student and staff management, reporting, and oversight functions
- Nurse: Medical operations, health records, and direct patient care
- Parent: Limited access to own children's records and communication features

**Data Access Rules:**
- Users can only access data relevant to their role and responsibilities
- Parents can only view information related to their registered children
- Medical staff can access all student health information within their scope
- Audit trails must be maintained for all data access and modifications

**Session Management:**
- User sessions expire after 8 hours of inactivity
- Concurrent sessions allowed but tracked for security monitoring
- Password changes require immediate re-authentication
- Failed login attempts trigger temporary account lockout after 5 attempts

#### 3.3 Data Integrity Rules

**Student Information:**
- Student codes are system-generated and immutable
- Each student must be assigned to exactly one active class
- Parent-student relationships are mandatory and cannot be deleted
- Student records cannot be permanently deleted (soft delete only)

**Medical Records:**
- All medical records must have associated timestamps and staff identification
- Medical requests require valid student and medication information
- Medication schedules cannot overlap for the same student and medication
- Health profile updates must maintain historical versions

### 4. Technical Specifications

#### 4.1 System Requirements

**Minimum Server Requirements:**
- **CPU**: 4-core processor (Intel i5 or AMD equivalent)
- **RAM**: 8GB minimum, 16GB recommended
- **Storage**: 100GB SSD for application and database
- **Network**: 100Mbps internet connection
- **OS**: Windows Server 2019+ or Ubuntu 20.04+

**Database Requirements:**
- **SQL Server**: 2019 Express (development) or Standard/Enterprise (production)
- **Storage**: 50GB minimum for database files
- **Backup**: Daily automated backups with 30-day retention
- **Performance**: Regular index maintenance and query optimization

**Client Requirements:**
- **Browser**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **JavaScript**: Enabled with modern ES6+ support
- **Screen Resolution**: 1024x768 minimum, 1920x1080 recommended
- **Internet**: Stable broadband connection (5Mbps minimum)

#### 4.2 Security Specifications

**Authentication Security:**
- **Password Policy**: Minimum 8 characters with complexity requirements
- **JWT Tokens**: 24-hour expiration with secure signing keys
- **OTP Security**: 6-digit codes with 5-minute expiration
- **Session Security**: Secure cookie handling with HttpOnly flags

**Data Security:**
- **Encryption**: TLS 1.3 for data in transit
- **Database**: Encrypted connections and sensitive data encryption at rest
- **File Storage**: Secure cloud storage with access controls
- **Audit Logging**: Comprehensive logging of all security-relevant events

**Network Security:**
- **HTTPS**: Enforced for all client-server communication
- **CORS**: Configured for specific allowed origins
- **Rate Limiting**: API request throttling to prevent abuse
- **Firewall**: Network-level protection with restricted port access

#### 4.3 Performance Specifications

**Response Time Requirements:**
- **Page Load**: < 3 seconds for initial page load
- **API Responses**: < 500ms for standard CRUD operations
- **Search Operations**: < 2 seconds for complex queries
- **File Uploads**: Progress indicators for uploads > 1MB

**Scalability Targets:**
- **Concurrent Users**: 100+ simultaneous users
- **Database Records**: 10,000+ students with full health histories
- **File Storage**: 10GB+ of images and documents
- **API Throughput**: 1000+ requests per minute

**Availability Requirements:**
- **Uptime**: 99.5% availability during school hours
- **Backup Recovery**: < 4 hours for full system restoration
- **Maintenance Windows**: Scheduled during non-school hours
- **Monitoring**: 24/7 system health monitoring and alerting

---

## Conclusion

The School Medical Management System (SMMS) represents a comprehensive solution for managing student health records, medical operations, and communication between school medical staff and parents. The system successfully implements modern software architecture principles with clean separation of concerns, robust security measures, and user-friendly interfaces.

**Key Achievements:**
- **Comprehensive Health Management**: Complete student health tracking from basic profiles to complex medical treatments
- **Role-Based Security**: Sophisticated authentication and authorization system ensuring data privacy and access control
- **Real-Time Communication**: Instant notifications and updates for critical medical situations
- **Scalable Architecture**: Clean architecture design supporting future enhancements and scaling requirements
- **User-Centric Design**: Intuitive interfaces tailored to different user roles and responsibilities

**Future Enhancement Opportunities:**
- Mobile application development for improved accessibility
- Advanced analytics and reporting capabilities
- Integration with external medical systems and databases
- Multi-language support for international deployment
- Enhanced workflow automation and AI-powered insights

This system provides a solid foundation for school medical management while maintaining flexibility for future growth and adaptation to changing healthcare requirements in educational environments.
```
