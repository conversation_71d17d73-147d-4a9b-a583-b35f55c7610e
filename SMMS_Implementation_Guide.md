# 🛠️ SMMS Implementation Guide & Technical Specifications

## 📋 Table of Contents
1. [Development Environment Setup](#development-environment-setup)
2. [Database Implementation](#database-implementation)
3. [API Implementation Details](#api-implementation-details)
4. [Frontend Implementation](#frontend-implementation)
5. [Security Implementation](#security-implementation)
6. [Deployment Architecture](#deployment-architecture)
7. [Testing Strategy](#testing-strategy)
8. [Maintenance & Support](#maintenance--support)

---

## 🔧 Development Environment Setup

### 1. Backend Development Environment

**📋 Prerequisites:**
```bash
# Required Software Versions
- .NET 8.0 SDK or later
- Visual Studio 2022 or VS Code
- SQL Server 2019+ or SQL Server Express
- Git for version control
- Postman for API testing
```

**⚙️ Project Structure:**
```
SMMS.API/
├── SMMS.API/                 # Web API Layer
│   ├── Controllers/          # API Controllers
│   ├── Program.cs           # Application entry point
│   ├── appsettings.json     # Configuration
│   └── Config/              # Configuration files
├── SMMS.Application/        # Business Logic Layer
│   ├── Services/            # Business services
│   ├── DataObject/          # DTOs
│   └── Helpers/             # Utility classes
├── SMMS.Domain/             # Domain Layer
│   ├── Entity/              # Domain entities
│   ├── Enum/                # Enumerations
│   └── Interface/           # Contracts
└── SMMS.Infrastructure/     # Data Access Layer
    ├── Context/             # Database context
    ├── Implements/          # Repository implementations
    └── Migrations/          # Database migrations
```

**🔧 Configuration Setup:**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=SMMS_DB;Trusted_Connection=true;MultipleActiveResultSets=true"
  },
  "JwtSettings": {
    "SecretKey": "your-super-secret-key-here",
    "Issuer": "SMMS-API",
    "Audience": "SMMS-Client",
    "ExpirationInHours": 24
  },
  "Firebase": {
    "ProjectId": "smms-otp",
    "ServiceAccountPath": "Config/firebase-service-account.json"
  },
  "Cloudinary": {
    "CloudName": "your-cloud-name",
    "ApiKey": "your-api-key",
    "ApiSecret": "your-api-secret"
  },
  "Redis": {
    "ConnectionString": "localhost:6379"
  }
}
```

### 2. Frontend Development Environment

**📋 Prerequisites:**
```bash
# Required Software
- Node.js 18+ and npm/yarn
- VS Code with React extensions
- Git for version control
- Chrome DevTools for debugging
```

**⚙️ Project Structure:**
```
FE/
├── public/                  # Static assets
├── src/
│   ├── components/          # Reusable components
│   │   ├── ui/             # Basic UI components
│   │   ├── layout/         # Layout components
│   │   └── context/        # React contexts
│   ├── pages/              # Page components
│   ├── services/           # API services
│   ├── types/              # TypeScript types
│   ├── utils/              # Utility functions
│   └── styles/             # Global styles
├── package.json            # Dependencies
├── tsconfig.json           # TypeScript config
├── tailwind.config.js      # Tailwind CSS config
└── vite.config.ts          # Vite configuration
```

**📦 Key Dependencies:**
```json
{
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "react-router-dom": "^6.8.0",
    "axios": "^1.3.0",
    "typescript": "^4.9.0",
    "@react-oauth/google": "^0.8.0",
    "lucide-react": "^0.220.0"
  },
  "devDependencies": {
    "@types/react": "^18.0.0",
    "@vitejs/plugin-react": "^3.1.0",
    "tailwindcss": "^3.2.0",
    "vite": "^4.1.0"
  }
}
```

---

## 🗄️ Database Implementation

### 3. Database Schema Implementation

**📊 Core Tables Structure:**

```sql
-- User Management Tables
CREATE TABLE [Role] (
    [Id] NVARCHAR(450) NOT NULL PRIMARY KEY,
    [RoleName] NVARCHAR(100) NOT NULL,
    [CreatedTime] DATETIMEOFFSET NOT NULL,
    [CreatedBy] NVARCHAR(450),
    [UpdatedTime] DATETIMEOFFSET NULL,
    [UpdatedBy] NVARCHAR(450) NULL,
    [DeletedTime] DATETIMEOFFSET NULL,
    [DeletedBy] NVARCHAR(450) NULL
);

CREATE TABLE [User] (
    [Id] NVARCHAR(450) NOT NULL PRIMARY KEY,
    [RoleId] NVARCHAR(450) NOT NULL,
    [Email] NVARCHAR(256) NOT NULL,
    [Phone] NVARCHAR(20),
    [Password] NVARCHAR(MAX) NOT NULL,
    [FullName] NVARCHAR(200) NOT NULL,
    [Image] NVARCHAR(MAX),
    [CreatedTime] DATETIMEOFFSET NOT NULL,
    [CreatedBy] NVARCHAR(450),
    [UpdatedTime] DATETIMEOFFSET NULL,
    [UpdatedBy] NVARCHAR(450) NULL,
    [DeletedTime] DATETIMEOFFSET NULL,
    [DeletedBy] NVARCHAR(450) NULL,
    CONSTRAINT [FK_User_Role] FOREIGN KEY ([RoleId]) REFERENCES [Role] ([Id])
);

-- Student Management Tables
CREATE TABLE [SchoolClass] (
    [Id] NVARCHAR(450) NOT NULL PRIMARY KEY,
    [ClassName] NVARCHAR(100) NOT NULL,
    [ClassRoom] NVARCHAR(50),
    [Quantity] INT NOT NULL DEFAULT 0,
    [CreatedTime] DATETIMEOFFSET NOT NULL,
    [CreatedBy] NVARCHAR(450),
    [UpdatedTime] DATETIMEOFFSET NULL,
    [UpdatedBy] NVARCHAR(450) NULL,
    [DeletedTime] DATETIMEOFFSET NULL,
    [DeletedBy] NVARCHAR(450) NULL
);

CREATE TABLE [Student] (
    [Id] NVARCHAR(450) NOT NULL PRIMARY KEY,
    [StudentNumber] INT IDENTITY(1,1) NOT NULL,
    [StudentCode] AS ('STD' + CAST([StudentNumber] AS VARCHAR(10))) PERSISTED,
    [ParentId] NVARCHAR(450) NOT NULL,
    [ClassId] NVARCHAR(450) NOT NULL,
    [FullName] NVARCHAR(200) NOT NULL,
    [Gender] NVARCHAR(10) NOT NULL,
    [DateOfBirth] DATETIME2 NOT NULL,
    [Image] NVARCHAR(MAX),
    [CreatedTime] DATETIMEOFFSET NOT NULL,
    [CreatedBy] NVARCHAR(450),
    [UpdatedTime] DATETIMEOFFSET NULL,
    [UpdatedBy] NVARCHAR(450) NULL,
    [DeletedTime] DATETIMEOFFSET NULL,
    [DeletedBy] NVARCHAR(450) NULL,
    CONSTRAINT [FK_Student_User] FOREIGN KEY ([ParentId]) REFERENCES [User] ([Id]),
    CONSTRAINT [FK_Student_SchoolClass] FOREIGN KEY ([ClassId]) REFERENCES [SchoolClass] ([Id])
);
```

**🔍 Indexing Strategy:**
```sql
-- Performance Indexes
CREATE INDEX [IX_User_Email] ON [User] ([Email]);
CREATE INDEX [IX_User_Phone] ON [User] ([Phone]);
CREATE INDEX [IX_Student_StudentCode] ON [Student] ([StudentCode]);
CREATE INDEX [IX_Student_ParentId] ON [Student] ([ParentId]);
CREATE INDEX [IX_MedicalRequest_StudentId] ON [MedicalRequest] ([StudentId]);
CREATE INDEX [IX_MedicalRequest_Status] ON [MedicalRequest] ([Status]);
CREATE INDEX [IX_HealthProfile_StudentId] ON [HealthProfile] ([StudentId]);

-- Composite Indexes for Common Queries
CREATE INDEX [IX_Student_Class_Active] ON [Student] ([ClassId], [DeletedTime]);
CREATE INDEX [IX_MedicalRequest_Student_Date] ON [MedicalRequest] ([StudentId], [StartDate], [EndDate]);
```

### 4. Entity Framework Configuration

**🏗️ DbContext Configuration:**
```csharp
public class DatabaseContext : DbContext
{
    public DatabaseContext(DbContextOptions<DatabaseContext> options) : base(options) { }

    // DbSets for all entities
    public virtual DbSet<User> User { get; set; }
    public virtual DbSet<Student> Student { get; set; }
    public virtual DbSet<MedicalRequest> MedicalRequest { get; set; }
    // ... other DbSets

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Configure relationships
        modelBuilder.Entity<User>()
            .HasOne(u => u.Role)
            .WithMany(r => r.Users)
            .HasForeignKey(u => u.RoleId)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<Student>()
            .HasOne(s => s.Parent)
            .WithMany(u => u.Students)
            .HasForeignKey(s => s.ParentId)
            .OnDelete(DeleteBehavior.Restrict);

        // Configure computed columns
        modelBuilder.Entity<Student>()
            .Property(s => s.StudentCode)
            .HasComputedColumnSql("'STD' + CAST([StudentNumber] AS VARCHAR(10))");

        // Configure soft delete global filter
        modelBuilder.Entity<User>()
            .HasQueryFilter(e => e.DeletedTime == null);
    }
}
```

---

## 🔌 API Implementation Details

### 5. Controller Implementation Patterns

**🎯 RESTful API Design:**
```csharp
[ApiController]
[Route("api/[controller]")]
public class MedicalController : ControllerBase
{
    private readonly IMedicalService _medicalService;
    private readonly ILogger<MedicalController> _logger;

    public MedicalController(IMedicalService medicalService, ILogger<MedicalController> logger)
    {
        _medicalService = medicalService;
        _logger = logger;
    }

    [HttpGet("stock")]
    [Authorize(Roles = "Admin,Manager,Nurse")]
    public async Task<ActionResult<List<MedicalStockResponse>>> GetMedicalStock()
    {
        try
        {
            var result = await _medicalService.GetAllMedicalStockAsync();
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving medical stock");
            return StatusCode(500, "Internal server error");
        }
    }

    [HttpPost("request")]
    [Authorize(Roles = "Admin,Manager,Nurse,Parent")]
    public async Task<IActionResult> CreateMedicalRequest([FromBody] CreateMedicalRequestRequest request)
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var result = await _medicalService.CreateMedicalRequestAsync(userId, request);
            
            if (!result)
                return BadRequest("Failed to create medical request");
                
            return Ok("Medical request created successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating medical request");
            return StatusCode(500, "Internal server error");
        }
    }
}
```

**🔒 Authentication Middleware:**
```csharp
public class JwtMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IConfiguration _configuration;

    public JwtMiddleware(RequestDelegate next, IConfiguration configuration)
    {
        _next = next;
        _configuration = configuration;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var token = context.Request.Headers["Authorization"]
            .FirstOrDefault()?.Split(" ").Last();

        if (token != null)
            AttachUserToContext(context, token);

        await _next(context);
    }

    private void AttachUserToContext(HttpContext context, string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_configuration["JwtSettings:SecretKey"]);
            
            tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _configuration["JwtSettings:Issuer"],
                ValidateAudience = true,
                ValidAudience = _configuration["JwtSettings:Audience"],
                ClockSkew = TimeSpan.Zero
            }, out SecurityToken validatedToken);

            var jwtToken = (JwtSecurityToken)validatedToken;
            var userId = jwtToken.Claims.First(x => x.Type == ClaimTypes.NameIdentifier).Value;
            
            // Attach user to context for use in controllers
            context.Items["UserId"] = userId;
        }
        catch
        {
            // Token validation failed
        }
    }
}
```

### 6. Service Layer Implementation

**🏗️ Service Pattern:**
```csharp
public class MedicalService : IMedicalService
{
    private readonly IRepositoryManager _repositoryManager;
    private readonly INotificationService _notificationService;
    private readonly ILogger<MedicalService> _logger;

    public MedicalService(
        IRepositoryManager repositoryManager,
        INotificationService notificationService,
        ILogger<MedicalService> logger)
    {
        _repositoryManager = repositoryManager;
        _notificationService = notificationService;
        _logger = logger;
    }

    public async Task<bool> CreateMedicalRequestAsync(string userId, CreateMedicalRequestRequest request)
    {
        try
        {
            // Validate student exists and user has permission
            var student = await _repositoryManager.StudentRepository
                .FindByCondition(s => s.Id == request.StudentId, false)
                .Include(s => s.Parent)
                .FirstOrDefaultAsync();

            if (student == null)
                throw new ArgumentException("Student not found");

            // Create medical request
            var medicalRequest = new MedicalRequest
            {
                Id = Guid.NewGuid().ToString(),
                StudentId = request.StudentId,
                UserId = userId,
                MedicationName = request.MedicationName,
                Dosage = request.Dosage,
                Unit = request.Unit,
                Frequency = request.Frequency,
                TotalQuantity = request.TotalQuantity,
                RemainingQuantity = request.TotalQuantity,
                TimeToAdminister = JsonSerializer.Serialize(request.TimeToAdminister),
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                Notes = request.Notes,
                Status = "Pending",
                CreatedTime = DateTimeOffset.UtcNow,
                CreatedBy = userId
            };

            _repositoryManager.MedicalRequestRepository.Create(medicalRequest);
            await _repositoryManager.SaveAsync();

            // Send notification to medical staff
            await _notificationService.NotifyMedicalStaffAsync(
                $"New medical request for {student.FullName}",
                medicalRequest.Id);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating medical request");
            return false;
        }
    }
}
```

---

## 🎨 Frontend Implementation

### 7. React Component Architecture

**🧩 Component Structure:**
```typescript
// Base Component Interface
interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Reusable Button Component
interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
}

export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  onClick,
  children,
  className = ''
}) => {
  const baseClasses = 'font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2';
  
  const variantClasses = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500',
    secondary: 'bg-gray-200 hover:bg-gray-300 text-gray-800 focus:ring-gray-500',
    danger: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500'
  };
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  return (
    <button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${
        disabled ? 'opacity-50 cursor-not-allowed' : ''
      } ${className}`}
      disabled={disabled || loading}
      onClick={onClick}
    >
      {loading ? (
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
          Loading...
        </div>
      ) : (
        children
      )}
    </button>
  );
};
```

**🔄 API Service Implementation:**
```typescript
// API Base Service
class ApiService {
  private baseURL = 'https://localhost:7172/api';
  
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const token = localStorage.getItem('token');
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(`${this.baseURL}${endpoint}`, config);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    return response.json();
  }

  async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint);
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async put<T>(endpoint: string, data: any): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    });
  }
}

// Medical Service Implementation
export class MedicalService extends ApiService {
  async getMedicalStock(): Promise<MedicalStock[]> {
    return this.get<MedicalStock[]>('/medical/stock');
  }

  async createMedicalRequest(request: CreateMedicalRequestRequest): Promise<boolean> {
    return this.post<boolean>('/medical/request', request);
  }

  async getDailyMedicationSchedule(date: string): Promise<DailyMedicationSchedule> {
    return this.get<DailyMedicationSchedule>(`/medical/request/daily/${date}`);
  }
}
```

---

## 🔒 Security Implementation

### 8. Authentication & Authorization

**🛡️ JWT Token Implementation:**
```csharp
public class JwtTokenGenerator : IJwtTokenGenerator
{
    private readonly IConfiguration _configuration;

    public JwtTokenGenerator(IConfiguration configuration)
    {
        _configuration = configuration;
    }

    public string GenerateToken(User user)
    {
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(_configuration["JwtSettings:SecretKey"]);
        
        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.NameIdentifier, user.Id),
            new Claim(ClaimTypes.Email, user.Email),
            new Claim(ClaimTypes.Name, user.FullName),
            new Claim(ClaimTypes.Role, user.Role.RoleName),
            new Claim("userId", user.Id),
            new Claim("roleId", user.RoleId)
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddHours(
                int.Parse(_configuration["JwtSettings:ExpirationInHours"])),
            Issuer = _configuration["JwtSettings:Issuer"],
            Audience = _configuration["JwtSettings:Audience"],
            SigningCredentials = new SigningCredentials(
                new SymmetricSecurityKey(key),
                SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        return tokenHandler.WriteToken(token);
    }
}
```

**🔐 Password Security:**
```csharp
public class PasswordService
{
    public static string HashPassword(string password)
    {
        return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt(12));
    }

    public static bool VerifyPassword(string password, string hashedPassword)
    {
        return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
    }

    public static bool IsPasswordStrong(string password)
    {
        // Minimum 8 characters, at least one uppercase, one lowercase, one digit, one special character
        var regex = new Regex(@"^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$");
        return regex.IsMatch(password);
    }
}
```

---

## 🚀 Deployment Architecture

### 9. Production Deployment

**🏗️ Infrastructure Requirements:**
```yaml
# Docker Compose Configuration
version: '3.8'
services:
  smms-api:
    build: ./BE/SMMS.API
    ports:
      - "5000:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ConnectionStrings__DefaultConnection=Server=db;Database=SMMS_DB;User=sa;Password=YourPassword123;
    depends_on:
      - db
      - redis

  smms-frontend:
    build: ./FE
    ports:
      - "3000:80"
    environment:
      - REACT_APP_API_URL=https://api.yourdomain.com

  db:
    image: mcr.microsoft.com/mssql/server:2019-latest
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=YourPassword123
    volumes:
      - sqldata:/var/opt/mssql

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"

volumes:
  sqldata:
```

**📊 Performance Monitoring:**
```csharp
// Application Insights Integration
public void ConfigureServices(IServiceCollection services)
{
    services.AddApplicationInsightsTelemetry();
    
    // Custom performance counters
    services.AddSingleton<IPerformanceCounter, PerformanceCounter>();
    
    // Health checks
    services.AddHealthChecks()
        .AddDbContext<DatabaseContext>()
        .AddRedis(Configuration.GetConnectionString("Redis"))
        .AddCheck<CustomHealthCheck>("custom-check");
}
```

---

## 🧪 Testing Strategy

### 10. Comprehensive Testing Approach

**🔬 Unit Testing:**
```csharp
[TestClass]
public class MedicalServiceTests
{
    private Mock<IRepositoryManager> _mockRepositoryManager;
    private Mock<INotificationService> _mockNotificationService;
    private MedicalService _medicalService;

    [TestInitialize]
    public void Setup()
    {
        _mockRepositoryManager = new Mock<IRepositoryManager>();
        _mockNotificationService = new Mock<INotificationService>();
        _medicalService = new MedicalService(
            _mockRepositoryManager.Object,
            _mockNotificationService.Object,
            Mock.Of<ILogger<MedicalService>>());
    }

    [TestMethod]
    public async Task CreateMedicalRequest_ValidRequest_ReturnsTrue()
    {
        // Arrange
        var request = new CreateMedicalRequestRequest
        {
            StudentId = "student-id",
            MedicationName = "Test Medicine",
            Dosage = "10mg",
            Frequency = 2
        };

        var student = new Student { Id = "student-id", FullName = "Test Student" };
        
        _mockRepositoryManager.Setup(r => r.StudentRepository
            .FindByCondition(It.IsAny<Expression<Func<Student, bool>>>(), false))
            .Returns(new List<Student> { student }.AsQueryable());

        // Act
        var result = await _medicalService.CreateMedicalRequestAsync("user-id", request);

        // Assert
        Assert.IsTrue(result);
        _mockRepositoryManager.Verify(r => r.SaveAsync(), Times.Once);
    }
}
```

**🎭 Integration Testing:**
```csharp
[TestClass]
public class MedicalControllerIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
{
    private readonly WebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public MedicalControllerIntegrationTests(WebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [TestMethod]
    public async Task GetMedicalStock_AuthenticatedUser_ReturnsOk()
    {
        // Arrange
        var token = await GetAuthTokenAsync();
        _client.DefaultRequestHeaders.Authorization = 
            new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);

        // Act
        var response = await _client.GetAsync("/api/medical/stock");

        // Assert
        response.EnsureSuccessStatusCode();
        var content = await response.Content.ReadAsStringAsync();
        Assert.IsNotNull(content);
    }
}
```

**📊 Testing Metrics:**
- **Unit Test Coverage**: >90%
- **Integration Test Coverage**: >80%
- **End-to-End Test Coverage**: >70%
- **Performance Test Coverage**: 100% of critical paths
- **Security Test Coverage**: 100% of authentication/authorization flows
